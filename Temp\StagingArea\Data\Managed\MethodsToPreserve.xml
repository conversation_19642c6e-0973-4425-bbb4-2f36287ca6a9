<linker>
	<assembly fullname="Assembly-CSharp" ignoreIfMissing="1">
		<type fullname="cronspray">
			<method name="Skip"/>
		</type>
		<type fullname="FarmingModmanager">
			<method name="Buttons"/>
			<method name="Home"/>
			<method name="Home"/>
			<method name="NextLevel"/>
			<method name="Pause"/>
			<method name="Restart"/>
			<method name="Restart"/>
			<method name="Resume"/>
			<method name="Steering"/>
			<method name="Tilt"/>
			<method name="ToggleController"/>
		</type>
		<type fullname="MainMenu">
			<method name="Btns"/>
			<method name="Careerlevel"/>
			<method name="Careerlevel"/>
			<method name="Careerlevel"/>
			<method name="Careerlevel"/>
			<method name="Careerlevel"/>
			<method name="Careerlevel"/>
			<method name="Careerlevel"/>
			<method name="Careerlevel"/>
			<method name="Careerlevel"/>
			<method name="Careerlevel"/>
			<method name="Farmlevel"/>
			<method name="Farmlevel"/>
			<method name="Farmlevel"/>
			<method name="Farmlevel"/>
			<method name="Farmlevel"/>
			<method name="Farmlevel"/>
			<method name="Farmlevel"/>
			<method name="Farmlevel"/>
			<method name="moregams"/>
			<method name="privacy"/>
			<method name="rateus"/>
			<method name="save"/>
			<method name="Steer"/>
			<method name="Tilt"/>
			<method name="Touchanlevel"/>
			<method name="Touchanlevel"/>
			<method name="Touchanlevel"/>
			<method name="Touchanlevel"/>
			<method name="Touchanlevel"/>
			<method name="yes"/>
		</type>
		<type fullname="manger2">
			<method name="home"/>
			<method name="home"/>
			<method name="home"/>
			<method name="home"/>
			<method name="next"/>
			<method name="pause"/>
			<method name="pause"/>
			<method name="restart"/>
			<method name="restart"/>
			<method name="restart"/>
			<method name="restart"/>
			<method name="resume"/>
			<method name="resume"/>
		</type>
		<type fullname="RCC_CustomizerExample">
			<method name="ChangeWheelsBySlider"/>
			<method name="ChangeWheelsBySlider"/>
			<method name="SetABS"/>
			<method name="SetABS"/>
			<method name="SetClutchThresholdBySlider"/>
			<method name="SetClutchThresholdBySlider"/>
			<method name="SetCounterSteeringByToggle"/>
			<method name="SetCounterSteeringByToggle"/>
			<method name="SetESP"/>
			<method name="SetESP"/>
			<method name="SetExhaustFlameByToggle"/>
			<method name="SetExhaustFlameByToggle"/>
			<method name="SetFrontCambersBySlider"/>
			<method name="SetFrontCambersBySlider"/>
			<method name="SetFrontSuspensionDistancesBySlider"/>
			<method name="SetFrontSuspensionDistancesBySlider"/>
			<method name="SetFrontSuspensionsSpringDamperBySlider"/>
			<method name="SetFrontSuspensionsSpringDamperBySlider"/>
			<method name="SetFrontSuspensionsSpringForceBySlider"/>
			<method name="SetFrontSuspensionsSpringForceBySlider"/>
			<method name="SetGearShiftingThresholdBySlider"/>
			<method name="SetGearShiftingThresholdBySlider"/>
			<method name="SetHeadlightColorByColorPicker"/>
			<method name="SetHeadlightColorByColorPicker"/>
			<method name="SetHeadlightColorByColorPicker"/>
			<method name="SetHeadlightColorByColorPicker"/>
			<method name="SetHeadlightColorByColorPicker"/>
			<method name="SetHeadlightColorByColorPicker"/>
			<method name="SetNOSByToggle"/>
			<method name="SetNOSByToggle"/>
			<method name="SetRearCambersBySlider"/>
			<method name="SetRearCambersBySlider"/>
			<method name="SetRearSuspensionDistancesBySlider"/>
			<method name="SetRearSuspensionDistancesBySlider"/>
			<method name="SetRearSuspensionsSpringDamperBySlider"/>
			<method name="SetRearSuspensionsSpringDamperBySlider"/>
			<method name="SetRearSuspensionsSpringForceBySlider"/>
			<method name="SetRearSuspensionsSpringForceBySlider"/>
			<method name="SetRevLimiterByToggle"/>
			<method name="SetRevLimiterByToggle"/>
			<method name="SetSH"/>
			<method name="SetSH"/>
			<method name="SetSHStrength"/>
			<method name="SetSHStrength"/>
			<method name="SetSmokeColorByColorPicker"/>
			<method name="SetSmokeColorByColorPicker"/>
			<method name="SetSmokeColorByColorPicker"/>
			<method name="SetSmokeColorByColorPicker"/>
			<method name="SetSmokeColorByColorPicker"/>
			<method name="SetSmokeColorByColorPicker"/>
			<method name="SetTCS"/>
			<method name="SetTCS"/>
			<method name="SetTransmission"/>
			<method name="SetTransmission"/>
			<method name="SetTurboByToggle"/>
			<method name="SetTurboByToggle"/>
			<method name="TogglePreviewExhaustFlameByToggle"/>
			<method name="TogglePreviewExhaustFlameByToggle"/>
			<method name="TogglePreviewSmokeByToggle"/>
			<method name="TogglePreviewSmokeByToggle"/>
		</type>
		<type fullname="SMGGameManager">
			<method name="Btns"/>
			<method name="changcontrol"/>
			<method name="gameplayrestart"/>
			<method name="home"/>
			<method name="home"/>
			<method name="next"/>
			<method name="ok"/>
			<method name="pause"/>
			<method name="PlayMusic"/>
			<method name="restart"/>
			<method name="restart"/>
			<method name="resume"/>
			<method name="Steer"/>
			<method name="StopMusic"/>
			<method name="Tilt"/>
		</type>
		<type fullname="startscene">
			<method name="accept"/>
			<method name="privacy"/>
		</type>
		<type fullname="WeatherSystem">
			<method name="Ad"/>
			<method name="Ad"/>
			<method name="Ad"/>
			<method name="WeatherAir"/>
			<method name="WeatherRain"/>
			<method name="Weathersunny"/>
		</type>
	</assembly>
	<assembly fullname="Unity.RenderPipelines.Core.Runtime" ignoreIfMissing="1">
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerButton">
			<method name="OnAction"/>
		</type>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerIntField">
			<method name="OnDecrement"/>
			<method name="OnDecrement"/>
			<method name="OnIncrement"/>
			<method name="OnIncrement"/>
		</type>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerPanel">
			<method name="OnScrollbarClicked"/>
			<method name="OnScrollbarClicked"/>
			<method name="ResetDebugManager"/>
		</type>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerUIntField">
			<method name="OnDecrement"/>
			<method name="OnDecrement"/>
			<method name="OnIncrement"/>
			<method name="OnIncrement"/>
		</type>
	</assembly>
	<assembly fullname="UnityEngine.AudioModule" ignoreIfMissing="1">
		<type fullname="UnityEngine.AudioSource">
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Stop"/>
		</type>
	</assembly>
	<assembly fullname="UnityEngine.CoreModule" ignoreIfMissing="1">
		<type fullname="UnityEngine.GameObject">
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
		</type>
	</assembly>
</linker>
