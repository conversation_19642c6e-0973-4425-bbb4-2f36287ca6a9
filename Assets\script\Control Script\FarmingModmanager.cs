

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class FarmingModmanager : MonoBehaviour
{
    public GameObject[] level;
    public Transform[] Position;
    public Text levelno;
    public GameObject Tractor;
    public GameObject Steeringactive, Buttonsactive, Tiltactive;
    public GameObject Steeriginactive, Buttonsinactive, Tiltinactive,TPS,Driver;
    public int controllerType = 0;

    void Start()
    {
        Time.timeScale = 1;
        InitializeLevels();
        level[MainMenu.levlno].SetActive(true);
        levelno.text = "Level : " + (MainMenu.levlno + 1);
        if (MainMenu.levlno == 0)
        {
            TPS.SetActive(true);
        }
        else
        {
            TPS.SetActive(false);
           Driver.SetActive(true);
        }
    }

    void InitializeLevels()
    {
        Tractor.transform.position = Position[MainMenu.levlno].position;
        Tractor.transform.rotation = Position[MainMenu.levlno].rotation;
    }

    public void NextLevel()
    {
        MainMenu.CompleteFarmLevel(MainMenu.levlno);

        if (MainMenu.levlno < level.Length - 1)
        {
            MainMenu.levlno++;
            PlayerPrefs.SetInt("Farm" + MainMenu.levlno, 1);
            PlayerPrefs.Save();
            SceneManager.LoadScene("Farming mod");
        }
        else
        {
            SceneManager.LoadScene("MAINMENU");
        }
    }
    public void Restart()
    {

        SceneManager.LoadScene("Farming mod");
    }
    public void Home()
    {
        SceneManager.LoadScene("MAINMENU");
    }
    public void Resume()
    {
        Time.timeScale = 1;
    }
    public void Pause()
    {
        Time.timeScale = 0;
    }
    void SetControlUI(GameObject activeControl)
    {
        Steeringactive.SetActive(activeControl == Steeringactive);
        Steeriginactive.SetActive(activeControl != Steeringactive);
        Buttonsactive.SetActive(activeControl == Buttonsactive);
        Buttonsinactive.SetActive(activeControl != Buttonsactive);
        Tiltactive.SetActive(activeControl == Tiltactive);
        Tiltinactive.SetActive(activeControl != Tiltactive);
    }

    public void Steering()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.SteeringWheel);
        SetControlUI(Steeringactive);
    }

    public void Buttons()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.TouchScreen);
        SetControlUI(Buttonsactive);
    }

    public void Tilt()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.Gyro);
        SetControlUI(Tiltactive);
    }
    public void ToggleController()
    {
        controllerType = (controllerType + 1) % 3;
        switch (controllerType)
        {
            case 0:
                RCC.SetMobileController(RCC_Settings.MobileController.TouchScreen);
                SetControlUI(Buttonsactive);
                break;
            case 1:
                RCC.SetMobileController(RCC_Settings.MobileController.Gyro);
                SetControlUI(Tiltactive);
                break;
            case 2:
                RCC.SetMobileController(RCC_Settings.MobileController.SteeringWheel);
                SetControlUI(Steeringactive);
                break;
            default:
                RCC.SetMobileController(RCC_Settings.MobileController.TouchScreen);
                SetControlUI(Buttonsactive);
                break;
        }
    }
    
}