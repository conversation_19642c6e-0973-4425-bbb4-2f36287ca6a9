{"System.Boolean": true, "Bee.TundraBackend.CSharpActionInvocationInformation": {"typeFullName": "PlayerBuildProgramLibrary.FeatureExtractor", "methodName": "Run", "assemblyLocation": "C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.45f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll", "targets": ["Library/Bee/artifacts/Android/Features/UnityEngine.VideoModule-FeaturesChecked.txt"], "inputs": ["Library/Bee/artifacts/Android/Features/FeatureCheckList.txt", "Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.VideoModule.dll"], "targetDirectories": []}}