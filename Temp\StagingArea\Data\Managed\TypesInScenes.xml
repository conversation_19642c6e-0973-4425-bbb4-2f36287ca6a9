<linker>
	<assembly fullname="Assembly-CSharp">
		<type fullname="AdmobManager" preserve="nothing"/>
		<type fullname="AdsController" preserve="nothing"/>
		<type fullname="AlertMessage" preserve="nothing"/>
		<type fullname="AutoTypeText" preserve="nothing"/>
		<type fullname="BannerAD" preserve="nothing"/>
		<type fullname="Birdtriiger" preserve="nothing"/>
		<type fullname="COMPAD" preserve="nothing"/>
		<type fullname="cronspray" preserve="nothing"/>
		<type fullname="Cubemovement" preserve="nothing"/>
		<type fullname="CutterAnimation" preserve="nothing"/>
		<type fullname="DistanceFromPlayer" preserve="nothing"/>
		<type fullname="DriverTrigger" preserve="nothing"/>
		<type fullname="Fail" preserve="nothing"/>
		<type fullname="FarmingModmanager" preserve="nothing"/>
		<type fullname="FirebaseAnalytics" preserve="nothing"/>
		<type fullname="FixedJoystick" preserve="nothing"/>
		<type fullname="GameOptimizer" preserve="nothing"/>
		<type fullname="GoogleMobileAdsConsentController" preserve="nothing"/>
		<type fullname="grasscollect" preserve="nothing"/>
		<type fullname="GrassCubeprocessormachine" preserve="nothing"/>
		<type fullname="grasscutter" preserve="nothing"/>
		<type fullname="grasspicker" preserve="nothing"/>
		<type fullname="ImageArrayDisplay" preserve="nothing"/>
		<type fullname="Invector.vCharacterController.vThirdPersonController" preserve="nothing"/>
		<type fullname="Invector.vCharacterController.vThirdPersonInput" preserve="nothing"/>
		<type fullname="linkattacher" preserve="nothing"/>
		<type fullname="LivingOcean" preserve="nothing"/>
		<type fullname="loadinglogic" preserve="nothing"/>
		<type fullname="MainMenu" preserve="nothing"/>
		<type fullname="manger2" preserve="nothing"/>
		<type fullname="Particlecontrol" preserve="nothing"/>
		<type fullname="PauseAd" preserve="nothing"/>
		<type fullname="player" preserve="nothing"/>
		<type fullname="playermain" preserve="nothing"/>
		<type fullname="Prefabpaintertool" preserve="nothing"/>
		<type fullname="RCC_Camera" preserve="nothing"/>
		<type fullname="RCC_CarControllerV3" preserve="nothing"/>
		<type fullname="RCC_ChangableWheels" preserve="nothing"/>
		<type fullname="RCC_CinematicCamera" preserve="nothing"/>
		<type fullname="RCC_ColorPickerBySliders" preserve="nothing"/>
		<type fullname="RCC_CustomizerExample" preserve="nothing"/>
		<type fullname="RCC_DashboardColors" preserve="nothing"/>
		<type fullname="RCC_DashboardInputs" preserve="nothing"/>
		<type fullname="RCC_Demo" preserve="nothing"/>
		<type fullname="RCC_DemoMaterials" preserve="nothing"/>
		<type fullname="RCC_DemoVehicles" preserve="nothing"/>
		<type fullname="RCC_Exhaust" preserve="nothing"/>
		<type fullname="RCC_FOVForCinematicCamera" preserve="nothing"/>
		<type fullname="RCC_GroundMaterials" preserve="nothing"/>
		<type fullname="RCC_HoodCamera" preserve="nothing"/>
		<type fullname="RCC_InfoLabel" preserve="nothing"/>
		<type fullname="RCC_InitialSettings" preserve="nothing"/>
		<type fullname="RCC_Light" preserve="nothing"/>
		<type fullname="RCC_Mirror" preserve="nothing"/>
		<type fullname="RCC_MobileButtons" preserve="nothing"/>
		<type fullname="RCC_MobileUIDrag" preserve="nothing"/>
		<type fullname="RCC_PoliceSiren" preserve="nothing"/>
		<type fullname="RCC_Records" preserve="nothing"/>
		<type fullname="RCC_SceneManager" preserve="nothing"/>
		<type fullname="RCC_Settings" preserve="nothing"/>
		<type fullname="RCC_Skidmarks" preserve="nothing"/>
		<type fullname="RCC_SkidmarksManager" preserve="nothing"/>
		<type fullname="RCC_Telemetry" preserve="nothing"/>
		<type fullname="RCC_TrailerAttachPoint" preserve="nothing"/>
		<type fullname="RCC_TruckTrailer" preserve="nothing"/>
		<type fullname="RCC_UI_Canvas_Modification" preserve="nothing"/>
		<type fullname="RCC_UI_Color" preserve="nothing"/>
		<type fullname="RCC_UI_MobileDrag" preserve="nothing"/>
		<type fullname="RCC_UI_Siren" preserve="nothing"/>
		<type fullname="RCC_UI_Spoiler" preserve="nothing"/>
		<type fullname="RCC_UI_Upgrade" preserve="nothing"/>
		<type fullname="RCC_UI_Wheel" preserve="nothing"/>
		<type fullname="RCC_UIController" preserve="nothing"/>
		<type fullname="RCC_UIDashboardButton" preserve="nothing"/>
		<type fullname="RCC_UIDashboardDisplay" preserve="nothing"/>
		<type fullname="RCC_UIJoystick" preserve="nothing"/>
		<type fullname="RCC_UISliderTextReader" preserve="nothing"/>
		<type fullname="RCC_UISteeringWheelController" preserve="nothing"/>
		<type fullname="RCC_Useless" preserve="nothing"/>
		<type fullname="RCC_VehicleUpgrade_Brake" preserve="nothing"/>
		<type fullname="RCC_VehicleUpgrade_Engine" preserve="nothing"/>
		<type fullname="RCC_VehicleUpgrade_Handling" preserve="nothing"/>
		<type fullname="RCC_VehicleUpgrade_Paint" preserve="nothing"/>
		<type fullname="RCC_VehicleUpgrade_PaintManager" preserve="nothing"/>
		<type fullname="RCC_VehicleUpgrade_Siren" preserve="nothing"/>
		<type fullname="RCC_VehicleUpgrade_SirenManager" preserve="nothing"/>
		<type fullname="RCC_VehicleUpgrade_Spoiler" preserve="nothing"/>
		<type fullname="RCC_VehicleUpgrade_SpoilerManager" preserve="nothing"/>
		<type fullname="RCC_VehicleUpgrade_UpgradeManager" preserve="nothing"/>
		<type fullname="RCC_VehicleUpgrade_WheelManager" preserve="nothing"/>
		<type fullname="RCC_WheelCollider" preserve="nothing"/>
		<type fullname="ScrollUV" preserve="nothing"/>
		<type fullname="SenseTraffic" preserve="nothing"/>
		<type fullname="SettingAD" preserve="nothing"/>
		<type fullname="SliderRelease" preserve="nothing"/>
		<type fullname="SMGGameManager" preserve="nothing"/>
		<type fullname="spraycontrol" preserve="nothing"/>
		<type fullname="startscene" preserve="nothing"/>
		<type fullname="targetaroundrotatecamera" preserve="nothing"/>
		<type fullname="TerrainPainter" preserve="nothing"/>
		<type fullname="Tractor" preserve="nothing"/>
		<type fullname="Tractorsense" preserve="nothing"/>
		<type fullname="trailercamera" preserve="nothing"/>
		<type fullname="TrollycubepickSystem" preserve="nothing"/>
		<type fullname="Tructorlinkcontroll" preserve="nothing"/>
		<type fullname="vThirdPersonCamera" preserve="nothing"/>
		<type fullname="Waypoint" preserve="nothing"/>
		<type fullname="WaypointMover" preserve="nothing"/>
		<type fullname="WaypointsHolder" preserve="nothing"/>
		<type fullname="WaypointWalker" preserve="nothing"/>
		<type fullname="WeatherSystem" preserve="nothing"/>
		<type fullname="wheelAi" preserve="nothing"/>
		<type fullname="wheelrotator" preserve="nothing"/>
		<type fullname="WPC" preserve="nothing"/>
		<type fullname="WPC_Waypoint" preserve="nothing"/>
	</assembly>
	<assembly fullname="Assembly-CSharp-firstpass">
		<type fullname="DG.Tweening.DOTweenAnimation" preserve="nothing"/>
	</assembly>
	<assembly fullname="Cinemachine">
		<type fullname="Cinemachine.CinemachineBrain" preserve="nothing"/>
		<type fullname="Cinemachine.CinemachineComposer" preserve="nothing"/>
		<type fullname="Cinemachine.CinemachinePipeline" preserve="nothing"/>
		<type fullname="Cinemachine.CinemachineTransposer" preserve="nothing"/>
		<type fullname="Cinemachine.CinemachineVirtualCamera" preserve="nothing"/>
		<type fullname="CinemachineShot" preserve="nothing"/>
		<type fullname="CinemachineTrack" preserve="nothing"/>
	</assembly>
	<assembly fullname="DOTween">
		<type fullname="DG.Tweening.Core.DOTweenSettings" preserve="nothing"/>
	</assembly>
	<assembly fullname="DOTweenPro">
		<type fullname="DG.Tweening.DOTweenVisualManager" preserve="nothing"/>
	</assembly>
	<assembly fullname="GoogleMobileAds.Common">
		<type fullname="GoogleMobileAds.Common.MobileAdsEventExecutor" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.InputSystem">
		<type fullname="UnityEngine.InputSystem.InputActionAsset" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.InputActionReference" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.InputSettings" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.InputSystemObject" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.RemoteInputPlayerConnection" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.UI.InputSystemUIInputModule" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.RenderPipelines.Core.Runtime">
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerBitField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerButton" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerCanvas" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerColor" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerContainer" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerEnumField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerEnumHistory" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerFloatField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerFoldout" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerGroup" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerHBox" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerIndirectFloatField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerIndirectToggle" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerIntField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerMessageBox" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerPanel" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerPersistentCanvas" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerRow" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerToggle" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerToggleHistory" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerUIntField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerValue" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerVBox" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerVector2" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerVector3" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerVector4" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.UIFoldout" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.TextMeshPro">
		<type fullname="TMPro.TextMeshProUGUI" preserve="nothing"/>
		<type fullname="TMPro.TMP_FontAsset" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.Timeline">
		<type fullname="UnityEngine.Timeline.ActivationPlayableAsset" preserve="nothing"/>
		<type fullname="UnityEngine.Timeline.ActivationTrack" preserve="nothing"/>
		<type fullname="UnityEngine.Timeline.AnimationTrack" preserve="nothing"/>
		<type fullname="UnityEngine.Timeline.AudioPlayableAsset" preserve="nothing"/>
		<type fullname="UnityEngine.Timeline.AudioTrack" preserve="nothing"/>
		<type fullname="UnityEngine.Timeline.GroupTrack" preserve="nothing"/>
		<type fullname="UnityEngine.Timeline.TimelineAsset" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine">
		<type fullname="UnityEditor.Animations.AnimatorController" preserve="nothing"/>
		<type fullname="UnityEditor.AudioManager" preserve="nothing"/>
		<type fullname="UnityEditor.InputManager" preserve="nothing"/>
		<type fullname="UnityEditor.MonoManager" preserve="nothing"/>
		<type fullname="UnityEditor.MonoScript" preserve="nothing"/>
		<type fullname="UnityEditor.Physics2DSettings" preserve="nothing"/>
		<type fullname="UnityEditor.PhysicsManager" preserve="nothing"/>
		<type fullname="UnityEditor.PlayerSettings" preserve="nothing"/>
		<type fullname="UnityEditor.TagManager" preserve="nothing"/>
		<type fullname="UnityEditor.TimeManager" preserve="nothing"/>
		<type fullname="UnityEditor.VFXManager" preserve="nothing"/>
		<type fullname="UnityEngine.AI.NavMeshObstacle" preserve="nothing"/>
		<type fullname="UnityEngine.Animation" preserve="nothing"/>
		<type fullname="UnityEngine.AnimationClip" preserve="nothing"/>
		<type fullname="UnityEngine.Animator" preserve="nothing"/>
		<type fullname="UnityEngine.Audio.AudioMixer" preserve="nothing"/>
		<type fullname="UnityEngine.Audio.AudioMixerGroup" preserve="nothing"/>
		<type fullname="UnityEngine.Audio.AudioMixerSnapshot" preserve="nothing"/>
		<type fullname="UnityEngine.AudioBehaviour" preserve="nothing"/>
		<type fullname="UnityEngine.AudioClip" preserve="nothing"/>
		<type fullname="UnityEngine.AudioListener" preserve="nothing"/>
		<type fullname="UnityEngine.AudioSource" preserve="nothing"/>
		<type fullname="UnityEngine.Avatar" preserve="nothing"/>
		<type fullname="UnityEngine.Behaviour" preserve="nothing"/>
		<type fullname="UnityEngine.BoxCollider" preserve="nothing"/>
		<type fullname="UnityEngine.Camera" preserve="nothing"/>
		<type fullname="UnityEngine.Canvas" preserve="nothing"/>
		<type fullname="UnityEngine.CanvasGroup" preserve="nothing"/>
		<type fullname="UnityEngine.CanvasRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.CapsuleCollider" preserve="nothing"/>
		<type fullname="UnityEngine.Collider" preserve="nothing"/>
		<type fullname="UnityEngine.Component" preserve="nothing"/>
		<type fullname="UnityEngine.ConfigurableJoint" preserve="nothing"/>
		<type fullname="UnityEngine.Cubemap" preserve="nothing"/>
		<type fullname="UnityEngine.Flare" preserve="nothing"/>
		<type fullname="UnityEngine.FlareLayer" preserve="nothing"/>
		<type fullname="UnityEngine.Font" preserve="nothing"/>
		<type fullname="UnityEngine.GameObject" preserve="nothing"/>
		<type fullname="UnityEngine.Joint" preserve="nothing"/>
		<type fullname="UnityEngine.LensFlare" preserve="nothing"/>
		<type fullname="UnityEngine.Light" preserve="nothing"/>
		<type fullname="UnityEngine.LightingSettings" preserve="nothing"/>
		<type fullname="UnityEngine.LightmapSettings" preserve="nothing"/>
		<type fullname="UnityEngine.LODGroup" preserve="nothing"/>
		<type fullname="UnityEngine.Material" preserve="nothing"/>
		<type fullname="UnityEngine.Mesh" preserve="nothing"/>
		<type fullname="UnityEngine.MeshCollider" preserve="nothing"/>
		<type fullname="UnityEngine.MeshFilter" preserve="nothing"/>
		<type fullname="UnityEngine.MeshRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.MonoBehaviour" preserve="nothing"/>
		<type fullname="UnityEngine.Motion" preserve="nothing"/>
		<type fullname="UnityEngine.Object" preserve="nothing"/>
		<type fullname="UnityEngine.ParticleSystem" preserve="nothing"/>
		<type fullname="UnityEngine.ParticleSystemRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.PhysicMaterial" preserve="nothing"/>
		<type fullname="UnityEngine.Playables.PlayableDirector" preserve="nothing"/>
		<type fullname="UnityEngine.QualitySettings" preserve="nothing"/>
		<type fullname="UnityEngine.RectTransform" preserve="nothing"/>
		<type fullname="UnityEngine.ReflectionProbe" preserve="nothing"/>
		<type fullname="UnityEngine.Renderer" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.GraphicsSettings" preserve="nothing"/>
		<type fullname="UnityEngine.RenderSettings" preserve="nothing"/>
		<type fullname="UnityEngine.Rigidbody" preserve="nothing"/>
		<type fullname="UnityEngine.RuntimeAnimatorController" preserve="nothing"/>
		<type fullname="UnityEngine.Shader" preserve="nothing"/>
		<type fullname="UnityEngine.SkinnedMeshRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.Sprite" preserve="nothing"/>
		<type fullname="UnityEngine.SpriteRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.Terrain" preserve="nothing"/>
		<type fullname="UnityEngine.TerrainCollider" preserve="nothing"/>
		<type fullname="UnityEngine.TerrainData" preserve="nothing"/>
		<type fullname="UnityEngine.TerrainLayer" preserve="nothing"/>
		<type fullname="UnityEngine.TextAsset" preserve="nothing"/>
		<type fullname="UnityEngine.Texture" preserve="nothing"/>
		<type fullname="UnityEngine.Texture2D" preserve="nothing"/>
		<type fullname="UnityEngine.TrailRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.Transform" preserve="nothing"/>
		<type fullname="UnityEngine.Video.VideoClip" preserve="nothing"/>
		<type fullname="UnityEngine.WheelCollider" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine.IMGUIModule">
		<type fullname="UnityEngine.GUISkin" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine.UI">
		<type fullname="UnityEngine.EventSystems.EventSystem" preserve="nothing"/>
		<type fullname="UnityEngine.EventSystems.EventTrigger" preserve="nothing"/>
		<type fullname="UnityEngine.EventSystems.StandaloneInputModule" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Button" preserve="nothing"/>
		<type fullname="UnityEngine.UI.CanvasScaler" preserve="nothing"/>
		<type fullname="UnityEngine.UI.ContentSizeFitter" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Dropdown" preserve="nothing"/>
		<type fullname="UnityEngine.UI.GraphicRaycaster" preserve="nothing"/>
		<type fullname="UnityEngine.UI.HorizontalLayoutGroup" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Image" preserve="nothing"/>
		<type fullname="UnityEngine.UI.InputField" preserve="nothing"/>
		<type fullname="UnityEngine.UI.LayoutElement" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Mask" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Outline" preserve="nothing"/>
		<type fullname="UnityEngine.UI.RawImage" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Scrollbar" preserve="nothing"/>
		<type fullname="UnityEngine.UI.ScrollRect" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Shadow" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Slider" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Text" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Toggle" preserve="nothing"/>
		<type fullname="UnityEngine.UI.VerticalLayoutGroup" preserve="nothing"/>
	</assembly>
</linker>
