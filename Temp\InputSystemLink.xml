<linker>
  <assembly fullname="Unity.InputSystem">
    <type fullname="Unity.XR.GoogleVr.DaydreamController" preserve="all"/>
    <type fullname="Unity.XR.GoogleVr.DaydreamHMD" preserve="all"/>
    <type fullname="Unity.XR.Oculus.Input.GearVRTrackedController" preserve="all"/>
    <type fullname="Unity.XR.Oculus.Input.OculusHMD" preserve="all"/>
    <type fullname="Unity.XR.Oculus.Input.OculusHMDExtended" preserve="all"/>
    <type fullname="Unity.XR.Oculus.Input.OculusRemote" preserve="all"/>
    <type fullname="Unity.XR.Oculus.Input.OculusTouchController" preserve="all"/>
    <type fullname="Unity.XR.Oculus.Input.OculusTrackingReference" preserve="all"/>
    <type fullname="Unity.XR.OpenVR.HandedViveTracker" preserve="all"/>
    <type fullname="Unity.XR.OpenVR.OpenVRControllerWMR" preserve="all"/>
    <type fullname="Unity.XR.OpenVR.OpenVRHMD" preserve="all"/>
    <type fullname="Unity.XR.OpenVR.OpenVROculusTouchController" preserve="all"/>
    <type fullname="Unity.XR.OpenVR.ViveLighthouse" preserve="all"/>
    <type fullname="Unity.XR.OpenVR.ViveTracker" preserve="all"/>
    <type fullname="Unity.XR.OpenVR.ViveWand" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Accelerometer" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.AmbientTemperatureSensor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.AndroidAccelerometer" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.AndroidAmbientTemperature" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.AndroidGamepad" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.AndroidGamepadWithDpadAxes" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.AndroidGamepadWithDpadButtons" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.AndroidGravitySensor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.AndroidGyroscope" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.AndroidJoystick" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.AndroidLightSensor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.AndroidLinearAccelerationSensor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.AndroidMagneticFieldSensor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.AndroidPressureSensor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.AndroidProximity" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.AndroidRelativeHumidity" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.AndroidRotationVector" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.AndroidStepCounter" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.DualShock4GamepadAndroid" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.LowLevel.AndroidCompensateDirectionProcessor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.LowLevel.AndroidCompensateRotationProcessor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.LowLevel.AndroidGameControllerState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.LowLevel.AndroidSensorState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Android.XboxOneGamepadAndroid" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.AttitudeSensor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Composites.AxisComposite" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Composites.ButtonWithOneModifier" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Composites.ButtonWithTwoModifiers" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Composites.OneModifierComposite" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Composites.TwoModifiersComposite" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Composites.Vector2Composite" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Composites.Vector3Composite" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.AnyKeyControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.AxisControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.ButtonControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.DeltaControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.DiscreteButtonControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.DoubleControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.DpadControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.DpadControl/DpadAxisControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.IntegerControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.KeyControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.QuaternionControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.StickControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.TouchControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.TouchPhaseControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.TouchPressControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.Vector2Control" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Controls.Vector3Control" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.DualShock.DualSenseGamepadHID" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.DualShock.DualShock3GamepadHID" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.DualShock.DualShock4GamepadHID" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.DualShock.DualShockGamepad" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.DualShock.LowLevel.DualSenseHIDInputReport" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.DualShock.LowLevel.DualShock3HIDInputReport" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.DualShock.LowLevel.DualShock4HIDInputReport" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.FastKeyboard" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.FastMouse" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.FastTouchscreen" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Gamepad" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.GravitySensor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Gyroscope" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.HID.HID" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.HumiditySensor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.IInputInteraction" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.IInputInteraction`1" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.InputBindingComposite" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.InputBindingComposite`1" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.InputControl`1" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.InputDevice" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.InputProcessor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.InputProcessor`1" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Interactions.HoldInteraction" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Interactions.MultiTapInteraction" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Interactions.PressInteraction" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Interactions.SlowTapInteraction" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Interactions.TapInteraction" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.iOS.DualSenseGampadiOS" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.iOS.DualShock4GampadiOS" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.iOS.iOSGameController" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.iOS.LowLevel.iOSGameControllerState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.iOS.LowLevel.iOSStepCounter" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.iOS.LowLevel.iOSStepCounterState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.iOS.XboxOneGampadiOS" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Joystick" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Keyboard" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.LightSensor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.LinearAccelerationSensor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.LowLevel.AccelerometerState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.LowLevel.AttitudeState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.LowLevel.GamepadState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.LowLevel.GravityState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.LowLevel.GyroscopeState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.LowLevel.IInputStateTypeInfo" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.LowLevel.JoystickState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.LowLevel.KeyboardState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.LowLevel.LinearAccelerationState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.LowLevel.MouseState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.LowLevel.PenState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.LowLevel.PointerState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.LowLevel.TouchscreenState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.LowLevel.TouchState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.MagneticFieldSensor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Mouse" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.OSX.LowLevel.NimbusPlusHIDInputReport" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.OSX.NimbusGamepadHid" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Pen" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Pointer" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.PressureSensor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Processors.AxisDeadzoneProcessor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Processors.ClampProcessor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Processors.CompensateDirectionProcessor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Processors.CompensateRotationProcessor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Processors.EditorWindowSpaceProcessor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Processors.InvertProcessor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Processors.InvertVector2Processor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Processors.InvertVector3Processor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Processors.NormalizeProcessor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Processors.NormalizeVector2Processor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Processors.NormalizeVector3Processor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Processors.ScaleProcessor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Processors.ScaleVector2Processor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Processors.ScaleVector3Processor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Processors.StickDeadzoneProcessor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.ProximitySensor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Sensor" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.StepCounter" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Switch.LowLevel.SwitchProControllerHIDInputState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Switch.SwitchProControllerHID" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.Touchscreen" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.TrackedDevice" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.WebGL.LowLevel.WebGLGamepadState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.WebGL.WebGLGamepad" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.WebGL.WebGLJoystick" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.XInput.LowLevel.XInputControllerWindowsState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.XInput.XInputController" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.XInput.XInputControllerWindows" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.XR.BoneControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.XR.EyesControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.XR.PoseControl" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.XR.PoseState" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.XR.XRController" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.XR.XRControllerWithRumble" preserve="all"/>
    <type fullname="UnityEngine.InputSystem.XR.XRHMD" preserve="all"/>
    <type fullname="UnityEngine.XR.WindowsMR.Input.HololensHand" preserve="all"/>
    <type fullname="UnityEngine.XR.WindowsMR.Input.WMRHMD" preserve="all"/>
    <type fullname="UnityEngine.XR.WindowsMR.Input.WMRSpatialController" preserve="all"/>
  </assembly>
</linker>
