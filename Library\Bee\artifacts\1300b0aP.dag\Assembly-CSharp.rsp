-target:library
-out:"Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll"
-refout:"Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.ref.dll"
-define:UNITY_2021_3_45
-define:UNITY_2021_3
-define:UNITY_2021
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_UNET
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_UNET
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_RUNTIME_PERMISSIONS
-define:ENABLE_ENGINE_CODE_STRIPPING
-define:ENABLE_ONSCREEN_KEYBOARD
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:PLATFORM_ANDROID
-define:TEXTCORE_1_0_OR_NEWER
-define:UNITY_ANDROID
-define:UNITY_ANDROID_API
-define:ENABLE_EGL
-define:ENABLE_NETWORK
-define:ENABLE_RUNTIME_GI
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:UNITY_CAN_SHOW_SPLASH_SCREEN
-define:UNITY_HAS_GOOGLEVR
-define:UNITY_HAS_TANGO
-define:ENABLE_SPATIALTRACKING
-define:ENABLE_ETC_COMPRESSION
-define:PLATFORM_EXTENDS_VULKAN_DEVICE
-define:PLATFORM_HAS_MULTIPLE_SWAPCHAINS
-define:UNITY_ANDROID_SUPPORTS_SHADOWFILES
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:ENABLE_UNITYADS_RUNTIME
-define:UNITY_UNITYADS_API
-define:ENABLE_IL2CPP
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:BCG_RCC
-define:Admob_Simple_Rizwan
-define:DOTWEEN
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/Firebase/Plugins/Firebase.Analytics.dll"
-r:"Assets/Firebase/Plugins/Firebase.App.dll"
-r:"Assets/Firebase/Plugins/Firebase.Platform.dll"
-r:"Assets/Firebase/Plugins/Firebase.TaskExtension.dll"
-r:"Assets/Firebase/Plugins/Google.MiniJson.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Android.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Common.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Core.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Ump.Android.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Ump.dll"
-r:"Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll"
-r:"Assets/Plugins/Demigiant/DOTween/DOTween.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ProfilerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIElementsNativeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UNETModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.XRModule.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp-firstpass.ref.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/Cinemachine.ref.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ref.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/Unity.ProBuilder.ref.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.ref.dll"
"Assets/#_AdManager_#/Extra Scripts/DontDestroyOnLoad.cs"
"Assets/#_AdManager_#/Extra Scripts/PluginManager.cs"
"Assets/#_AdManager_#/Extra Scripts/RewardReadyCheck.cs"
"Assets/#_AdManager_#/Extra Scripts/SceneLoad.cs"
"Assets/#_AdManager_#/Extra Scripts/Testingads.cs"
"Assets/#_AdManager_#/Firebase_Scripts/AlertMessage/AlertMessage.cs"
"Assets/#_AdManager_#/Firebase_Scripts/FirebaseAnalyticsHandler.cs"
"Assets/#_AdManager_#/Firebase_Scripts/FireBaseInitializer.cs"
"Assets/#_AdManager_#/Firebase_Scripts/FirebaseRemoteConfigHandler.cs"
"Assets/#_AdManager_#/GameAnalytics/GameAnalyticsInitializer.cs"
"Assets/#_AdManager_#/InApp_Purchases/Scripts/IAP_Controller.cs"
"Assets/#_AdManager_#/InApp_Review/Scripts/InAppReview.cs"
"Assets/#_AdManager_#/InApp_Update/Scripts/InAppUpdate.cs"
"Assets/#_AdManager_#/SKAN & ATT/AttPermissionRequest.cs"
"Assets/#_AdManager_#/_ADS/Admob/AdmobManager.cs"
"Assets/#_AdManager_#/_ADS/Admob/GoogleMobileAdsConsentController.cs"
"Assets/#_AdManager_#/_ADS/AdsController.cs"
"Assets/#_AdManager_#/_ADS/GameAD/BannerAD.cs"
"Assets/#_AdManager_#/_ADS/GameAD/COMPAD.cs"
"Assets/#_AdManager_#/_ADS/GameAD/LoadingAD.cs"
"Assets/#_AdManager_#/_ADS/GameAD/LoadingLogic.cs"
"Assets/#_AdManager_#/_ADS/GameAD/PauseAd.cs"
"Assets/#_AdManager_#/_ADS/GameAD/RECtangleAD.cs"
"Assets/#_AdManager_#/_ADS/GameAD/SettingAD.cs"
"Assets/#_AdManager_#/_ADS/MAX Mediation/MaxMediation.cs"
"Assets/#_AdManager_#/_ADS/MAX Mediation/Mintegral Roas/MintegralRoas.cs"
"Assets/#_AdManager_#/_ADS/UnityAds/UnityAdsManager.cs"
"Assets/3DAssets/Archanor/Sci-Fi Arsenal/InteractiveDemo/Demo Scripts/SciFiBeamScript.cs"
"Assets/3DAssets/Archanor/Sci-Fi Arsenal/InteractiveDemo/Demo Scripts/SciFiButtonScript.cs"
"Assets/3DAssets/Archanor/Sci-Fi Arsenal/InteractiveDemo/Demo Scripts/SciFiDragMouseOrbit.cs"
"Assets/3DAssets/Archanor/Sci-Fi Arsenal/InteractiveDemo/Demo Scripts/SciFiFireProjectile.cs"
"Assets/3DAssets/Archanor/Sci-Fi Arsenal/InteractiveDemo/Demo Scripts/SciFiLoadSceneOnClick.cs"
"Assets/3DAssets/Archanor/Sci-Fi Arsenal/InteractiveDemo/Demo Scripts/SciFiLoadSceneOnClick2.cs"
"Assets/3DAssets/Archanor/Sci-Fi Arsenal/InteractiveDemo/Demo Scripts/SciFiLoopScript.cs"
"Assets/3DAssets/Archanor/Sci-Fi Arsenal/InteractiveDemo/Demo Scripts/SciFiProjectileScript.cs"
"Assets/3DAssets/Archanor/Sci-Fi Arsenal/Sci-Fi Effects/Scripts/SciFiLightFade.cs"
"Assets/3DAssets/Archanor/Sci-Fi Arsenal/Sci-Fi Effects/Scripts/SciFiLightFlicker.cs"
"Assets/3DAssets/Archanor/Sci-Fi Arsenal/Sci-Fi Effects/Scripts/SciFiRotation.cs"
"Assets/Asset Bundle/Game Manager/POPULATION/ObjectRangeManager.cs"
"Assets/Asset Bundle/Game Manager/POPULATION/WalkerCollisionHandler.cs"
"Assets/Asset Bundle/Game Manager/POPULATION/WaypointWalker.cs"
"Assets/Asset Bundle/Game Manager/POPULATION/WPC.cs"
"Assets/Asset Bundle/Game Manager/POPULATION/WPC_Editor.cs"
"Assets/Asset Bundle/Game Manager/POPULATION/WPC_Waypoint.cs"
"Assets/EpicVictoryEffects (1)/EpicVictoryEffects/Scripts/starFxController.cs"
"Assets/EpicVictoryEffects (1)/EpicVictoryEffects/Scripts/vfxController.cs"
"Assets/EpicVictoryEffects/Scripts/starFxControllerMy.cs"
"Assets/Extra 1/WeatherSystem.cs"
"Assets/GameMy/Water_Fountain/Scripts/ScrollUV.cs"
"Assets/Invector-3rdPersonController_LITE/3D Models/Others/HealthItem/vAnimateUV.cs"
"Assets/Invector-3rdPersonController_LITE/Scripts/Camera/vThirdPersonCamera.cs"
"Assets/Invector-3rdPersonController_LITE/Scripts/CharacterController/vThirdPersonAnimator.cs"
"Assets/Invector-3rdPersonController_LITE/Scripts/CharacterController/vThirdPersonController.cs"
"Assets/Invector-3rdPersonController_LITE/Scripts/CharacterController/vThirdPersonInput.cs"
"Assets/Invector-3rdPersonController_LITE/Scripts/CharacterController/vThirdPersonMotor.cs"
"Assets/Invector-3rdPersonController_LITE/Scripts/Generic/Utils/vComment.cs"
"Assets/Invector-3rdPersonController_LITE/Scripts/Generic/Utils/vExtensions.cs"
"Assets/Invector-3rdPersonController_LITE/Scripts/Generic/Utils/vPickupItem.cs"
"Assets/Joystick Pack/Examples/JoystickPlayerExample.cs"
"Assets/Joystick Pack/Examples/JoystickSetterExample.cs"
"Assets/Joystick Pack/Scripts/Base/Joystick.cs"
"Assets/Joystick Pack/Scripts/Joysticks/DynamicJoystick.cs"
"Assets/Joystick Pack/Scripts/Joysticks/FixedJoystick.cs"
"Assets/Joystick Pack/Scripts/Joysticks/FloatingJoystick.cs"
"Assets/Joystick Pack/Scripts/Joysticks/VariableJoystick.cs"
"Assets/kachra/kachra/kachra/Birdtriiger.cs"
"Assets/kachra/kachra/kachra/ButtonChildToggle.cs"
"Assets/kachra/kachra/kachra/DistanceFromPlayer.cs"
"Assets/kachra/kachra/kachra/GameOptimizer.cs"
"Assets/kachra/kachra/kachra/ImageArrayDisplay.cs"
"Assets/kachra/kachra/kachra/RCCOrbitReset.cs"
"Assets/kachra/kachra/kachra/SaveButtonController.cs"
"Assets/kachra/kachra/kachra/SenseTraffic.cs"
"Assets/kachra/kachra/kachra/Tractorsense.cs"
"Assets/Magic Spells and Popup effects/Particles/LightAnimation.cs"
"Assets/Magic Spells and Popup effects/Particles/Rotator.cs"
"Assets/Magic Spells and Popup effects/Script/DemoController.cs"
"Assets/MeshCombiner/Scripts/MeshCombiner.cs"
"Assets/PrefabInstances/PowerUps FullPackage/Scripts/PowerUpAnimation.cs"
"Assets/PrefabpainterTool/Prefabpaintertool.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_AIBrakeZone.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_AIBrakeZonesContainer.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_AICarController.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_AIO.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_AIWaypointsContainer.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_APIExample.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_AssetPaths.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Caliper.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Camera.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_CameraCarSelection.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_CarControllerV3.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_CarSelectionExample.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_ChangableWheels.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_CharacterController.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_CheckUp.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_CinematicCamera.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_ColorPickerBySliders.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Core.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_CrashHammer.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_CrashPress.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_CrashShredder.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Customization.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_CustomizationApplier.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_CustomizationDemo.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_CustomizationLoadout.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_CustomizationManager.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_CustomizationTrigger.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_CustomizerExample.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Damage.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_DashboardColors.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_DashboardInputs.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_DashboardObjects.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Demo.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_DemoMaterials.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_DemoVehicles.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_DetachablePart.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Emission.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Exhaust.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_FeedVehicleExample.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_FixedCamera.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_FOVForCinematicCamera.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_FuelStation.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_GetBounds.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_GroundMaterials.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_HoodCamera.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_InfoLabel.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_InitialSettings.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_InputActions.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_InputManager.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Inputs.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Installation.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Joint.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_LevelLoader.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Light.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_LOD.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Mirror.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_MobileButtons.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_MobileUIDrag.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_PlayerPrefsX.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_PoliceSiren.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Recorder.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Records.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_RepairStation.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_SceneManager.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_SetLayer.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Settings.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_ShadowRotConst.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_ShowroomCamera.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Singleton.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Skidmarks.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_SkidmarksManager.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Spawner.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_SpeedLimiter.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_SuspensionArm.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Telemetry.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Teleporter.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_TrailerAttachPoint.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_TruckTrailer.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_UIController.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_UIDashboardButton.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_UIDashboardDisplay.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_UIJoystick.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_UISliderTextReader.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_UISteeringWheelController.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_UI_Canvas_Modification.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_UI_Color.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_UI_MobileDrag.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_UI_Siren.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_UI_Spoiler.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_UI_Upgrade.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_UI_Wheel.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Useless.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_VehicleUpgrade_Brake.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_VehicleUpgrade_Engine.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_VehicleUpgrade_Handling.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_VehicleUpgrade_Paint.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_VehicleUpgrade_PaintManager.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_VehicleUpgrade_Siren.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_VehicleUpgrade_SirenManager.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_VehicleUpgrade_Spoiler.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_VehicleUpgrade_SpoilerManager.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_VehicleUpgrade_UpgradeManager.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_VehicleUpgrade_WheelManager.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Version.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_Waypoint.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_WheelCamera.cs"
"Assets/RealisticCarControllerV3/Scripts/RCC_WheelCollider.cs"
"Assets/script/Animationtexture.cs"
"Assets/script/AutoTypeText.cs"
"Assets/script/brake.cs"
"Assets/script/Changetexture (1).cs"
"Assets/script/Control Script/Cubemovement.cs"
"Assets/script/Control Script/CutterAnimation.cs"
"Assets/script/Control Script/DesireMachinetrue.cs"
"Assets/script/Control Script/DriverTrigger.cs"
"Assets/script/Control Script/FarmingModmanager.cs"
"Assets/script/Control Script/grasscollect.cs"
"Assets/script/Control Script/GrassCubeprocessormachine.cs"
"Assets/script/Control Script/grasscutter.cs"
"Assets/script/Control Script/grasspicker.cs"
"Assets/script/Control Script/linkattacher.cs"
"Assets/script/Control Script/Machinecameracontroll.cs"
"Assets/script/Control Script/Particlecontrol.cs"
"Assets/script/Control Script/SmoothTurnSplineDemo.cs"
"Assets/script/Control Script/spraycontrol.cs"
"Assets/script/Control Script/targetaroundrotatecamera.cs"
"Assets/script/Control Script/TerrainPainter.cs"
"Assets/script/Control Script/Tractor.cs"
"Assets/script/Control Script/TractorTrailController.cs"
"Assets/script/Control Script/trailercamera.cs"
"Assets/script/Control Script/TrailSplinePainter.cs"
"Assets/script/Control Script/Tructorlinkcontroll.cs"
"Assets/script/cronspray.cs"
"Assets/script/FadeScreenManager.cs"
"Assets/script/FirebaseAnalytics.cs"
"Assets/script/GDPRScript.cs"
"Assets/script/LevelLock.cs"
"Assets/script/LevelLockImages.cs"
"Assets/script/LivingOcean.cs"
"Assets/script/loadinglogic.cs"
"Assets/script/MainMenu.cs"
"Assets/script/manger2.cs"
"Assets/script/mode2lock.cs"
"Assets/script/player.cs"
"Assets/script/SMGGameManager.cs"
"Assets/script/startscene.cs"
"Assets/script/TrollycubepickSystem.cs"
"Assets/script/WC15PRO.cs"
"Assets/script/WeatherManager.cs"
"Assets/script/wheelrotator.cs"
"Assets/Scripts/MobileUIController.cs"
"Assets/Scripts/UIButtonConnector.cs"
"Assets/Touchen Script/Fail.cs"
"Assets/Touchen Script/playermain.cs"
"Assets/Touchen Script/SliderRelease.cs"
"Assets/Touchen Script/targetcam.cs"
"Assets/Touchen Script/TimerSeconds.cs"
"Assets/Touchen Script/wheelAi.cs"
"Assets/WaypointsSystem/_EasyWaypointSystem_/IgnoreCollisions.cs"
"Assets/WaypointsSystem/_EasyWaypointSystem_/Waypoint.cs"
"Assets/WaypointsSystem/_EasyWaypointSystem_/WaypointMover.cs"
"Assets/WaypointsSystem/_EasyWaypointSystem_/WaypointsHolder.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.AdditionalFile.txt"