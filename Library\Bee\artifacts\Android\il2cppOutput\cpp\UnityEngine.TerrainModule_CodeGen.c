﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void Microsoft.CodeAnalysis.EmbeddedAttribute::.ctor()
extern void EmbeddedAttribute__ctor_mBB01735E786E7770A1B2ECAA65D1219B6D039DA5 (void);
// 0x00000002 System.Void System.Runtime.CompilerServices.IsReadOnlyAttribute::.ctor()
extern void IsReadOnlyAttribute__ctor_mC20B41FF7D952FD1FE27E289E221F34681837870 (void);
// 0x00000003 System.Int32 UnityEngine.TerrainData::GetBoundaryValue(UnityEngine.TerrainData/BoundaryValueType)
extern void TerrainData_GetBoundaryValue_mA9217CC15BBC958C9F7071B96CE74769EFDC322E (void);
// 0x00000004 System.Void UnityEngine.TerrainData::.ctor()
extern void TerrainData__ctor_m1B68EB89248D5706C2528F47279812F824E27A2E (void);
// 0x00000005 System.Void UnityEngine.TerrainData::Internal_Create(UnityEngine.TerrainData)
extern void TerrainData_Internal_Create_m79BF764CFF5F49D17E2BFC8B20F60B4CF70BE4E1 (void);
// 0x00000006 UnityEngine.Vector3 UnityEngine.TerrainData::get_size()
extern void TerrainData_get_size_mCD3977F344B9DEBFF61DD537D03FEB9473838DA5 (void);
// 0x00000007 System.Int32 UnityEngine.TerrainData::get_alphamapLayers()
extern void TerrainData_get_alphamapLayers_mF8A0A4F157F7C56354C5A6E3FABF9F230F410F69 (void);
// 0x00000008 System.Single[,,] UnityEngine.TerrainData::GetAlphamaps(System.Int32,System.Int32,System.Int32,System.Int32)
extern void TerrainData_GetAlphamaps_m2DEF5D2068D54BDAE78661483C1FC4936B06EA01 (void);
// 0x00000009 System.Single[,,] UnityEngine.TerrainData::Internal_GetAlphamaps(System.Int32,System.Int32,System.Int32,System.Int32)
extern void TerrainData_Internal_GetAlphamaps_m6891C19FF72B4394D6BAC3B098A3FCAC1FC6BF36 (void);
// 0x0000000A System.Int32 UnityEngine.TerrainData::get_alphamapResolution()
extern void TerrainData_get_alphamapResolution_mC5D1C8FF4A5AFFCCFCF1382FED0D1AD46563D6F8 (void);
// 0x0000000B System.Single UnityEngine.TerrainData::GetAlphamapResolutionInternal()
extern void TerrainData_GetAlphamapResolutionInternal_m5C312434763B8F0BD8DE760ACF439DFEFAC2F3E5 (void);
// 0x0000000C System.Int32 UnityEngine.TerrainData::get_Internal_alphamapResolution()
extern void TerrainData_get_Internal_alphamapResolution_mDA8EF6055A2022B3E1E4E6ECBF8DF4387DE8A930 (void);
// 0x0000000D System.Int32 UnityEngine.TerrainData::get_alphamapWidth()
extern void TerrainData_get_alphamapWidth_m07E5B04B08E87AC9F66D766B363000F94C8612D4 (void);
// 0x0000000E System.Int32 UnityEngine.TerrainData::get_alphamapHeight()
extern void TerrainData_get_alphamapHeight_m4A8273D6E0E3526A31E2669FBAB240353C086AED (void);
// 0x0000000F System.Void UnityEngine.TerrainData::SetAlphamaps(System.Int32,System.Int32,System.Single[,,])
extern void TerrainData_SetAlphamaps_m2FA8E29ADF4D1B2B8286ED0FE61E0CEB1663E423 (void);
// 0x00000010 System.Void UnityEngine.TerrainData::Internal_SetAlphamaps(System.Int32,System.Int32,System.Int32,System.Int32,System.Single[,,])
extern void TerrainData_Internal_SetAlphamaps_m87D35A2FED5E274AEBDF3ADB00A7FA477861E8FD (void);
// 0x00000011 UnityEngine.Terrain[] UnityEngine.TerrainData::get_users()
extern void TerrainData_get_users_m0C569F1AD5853CEBF3C572723A1CAD04AC8433C4 (void);
// 0x00000012 System.Void UnityEngine.TerrainData::.cctor()
extern void TerrainData__cctor_m525F8AF6DEDDACF640BD2D24767502121ED6D9B0 (void);
// 0x00000013 System.Void UnityEngine.TerrainData::get_size_Injected(UnityEngine.Vector3&)
extern void TerrainData_get_size_Injected_m0F56E87C4D7EDD1D84F038E4AF0F273D328CF661 (void);
// 0x00000014 UnityEngine.TerrainData UnityEngine.Terrain::get_terrainData()
extern void Terrain_get_terrainData_m3B6C1D89471A4E1C60FC19C168DB37A011B924FD (void);
// 0x00000015 System.Boolean UnityEngine.Terrain::get_allowAutoConnect()
extern void Terrain_get_allowAutoConnect_m4E9CB87D130BB118517C5504C8FB4A5CC3AA91D7 (void);
// 0x00000016 System.Int32 UnityEngine.Terrain::get_groupingID()
extern void Terrain_get_groupingID_mE52E78018126A5D00F837081287BE076E7709C24 (void);
// 0x00000017 System.Void UnityEngine.Terrain::SetNeighbors(UnityEngine.Terrain,UnityEngine.Terrain,UnityEngine.Terrain,UnityEngine.Terrain)
extern void Terrain_SetNeighbors_m2FFA89D199120125D264EF7EE0BC749A35514C1E (void);
// 0x00000018 UnityEngine.Terrain UnityEngine.Terrain::get_activeTerrain()
extern void Terrain_get_activeTerrain_mAE5A7FE933C2C1A57FC9542E9BFA315A413F224E (void);
// 0x00000019 UnityEngine.Terrain[] UnityEngine.Terrain::get_activeTerrains()
extern void Terrain_get_activeTerrains_mB90A9BC89764F626D13F3EF1420EA8D3E186B701 (void);
// 0x0000001A System.Void UnityEngine.Terrain::.ctor()
extern void Terrain__ctor_m11F03EC6C1E68752DDCAE8EF2DED99CFD939FCDC (void);
// 0x0000001B System.Void UnityEngine.TerrainCallbacks::InvokeHeightmapChangedCallback(UnityEngine.TerrainData,UnityEngine.RectInt,System.Boolean)
extern void TerrainCallbacks_InvokeHeightmapChangedCallback_m731ED939CBD563CCCE503062602DF5908205AD04 (void);
// 0x0000001C System.Void UnityEngine.TerrainCallbacks::InvokeTextureChangedCallback(UnityEngine.TerrainData,System.String,UnityEngine.RectInt,System.Boolean)
extern void TerrainCallbacks_InvokeTextureChangedCallback_mB508E8B7A884854AA01AE5B88AB33E1AE40F4318 (void);
// 0x0000001D System.Void UnityEngine.TerrainCallbacks/HeightmapChangedCallback::.ctor(System.Object,System.IntPtr)
extern void HeightmapChangedCallback__ctor_m6A7E4189E0A7A1B70EE73818B93B0FC9F613648C (void);
// 0x0000001E System.Void UnityEngine.TerrainCallbacks/HeightmapChangedCallback::Invoke(UnityEngine.Terrain,UnityEngine.RectInt,System.Boolean)
extern void HeightmapChangedCallback_Invoke_m63C1C93709641DBE02DCE9F71B7895C5793AF875 (void);
// 0x0000001F System.Void UnityEngine.TerrainCallbacks/TextureChangedCallback::.ctor(System.Object,System.IntPtr)
extern void TextureChangedCallback__ctor_m64076D799FEB79E3D6BE2C4EB33CD081A398F0EF (void);
// 0x00000020 System.Void UnityEngine.TerrainCallbacks/TextureChangedCallback::Invoke(UnityEngine.Terrain,System.String,UnityEngine.RectInt,System.Boolean)
extern void TextureChangedCallback_Invoke_m1194A44102843272B51A70C302EBDBC8214647DE (void);
// 0x00000021 System.Void UnityEngine.TerrainUtils.TerrainTileCoord::.ctor(System.Int32,System.Int32)
extern void TerrainTileCoord__ctor_m6B6744655B9C3BA9B1A92076F07002B4B4EB899A (void);
// 0x00000022 UnityEngine.Terrain UnityEngine.TerrainUtils.TerrainMap::GetTerrain(System.Int32,System.Int32)
extern void TerrainMap_GetTerrain_mFF9C935F05859DF70E95994E727565BD67CDD6FC (void);
// 0x00000023 UnityEngine.TerrainUtils.TerrainMap UnityEngine.TerrainUtils.TerrainMap::CreateFromPlacement(UnityEngine.Terrain,System.Predicate`1<UnityEngine.Terrain>,System.Boolean)
extern void TerrainMap_CreateFromPlacement_mC7822A5F4FC2A2CB119259A48F19D364ACEC5AE7 (void);
// 0x00000024 UnityEngine.TerrainUtils.TerrainMap UnityEngine.TerrainUtils.TerrainMap::CreateFromPlacement(UnityEngine.Vector2,UnityEngine.Vector2,System.Predicate`1<UnityEngine.Terrain>,System.Boolean)
extern void TerrainMap_CreateFromPlacement_m64B90ADBC1D3A1AE18CEC7D0B452377E10B2BCB5 (void);
// 0x00000025 System.Collections.Generic.Dictionary`2<UnityEngine.TerrainUtils.TerrainTileCoord,UnityEngine.Terrain> UnityEngine.TerrainUtils.TerrainMap::get_terrainTiles()
extern void TerrainMap_get_terrainTiles_m9EAA8FCB972C834E2093DDD49B26DBBA2E74A2AB (void);
// 0x00000026 System.Void UnityEngine.TerrainUtils.TerrainMap::.ctor()
extern void TerrainMap__ctor_mCDB47BA50D9D54E65754028F9CF8F91828FE616F (void);
// 0x00000027 System.Void UnityEngine.TerrainUtils.TerrainMap::AddTerrainInternal(System.Int32,System.Int32,UnityEngine.Terrain)
extern void TerrainMap_AddTerrainInternal_m507CE3A3F880B33CA2330F69464E3511D5B9BD71 (void);
// 0x00000028 System.Boolean UnityEngine.TerrainUtils.TerrainMap::TryToAddTerrain(System.Int32,System.Int32,UnityEngine.Terrain)
extern void TerrainMap_TryToAddTerrain_m03A05C883F317FD2E6956ADD6625409E8A90BE15 (void);
// 0x00000029 System.Void UnityEngine.TerrainUtils.TerrainMap::ValidateTerrain(System.Int32,System.Int32)
extern void TerrainMap_ValidateTerrain_m8D9B035B3851E0ED8BB5877BD11F63BA85029653 (void);
// 0x0000002A UnityEngine.TerrainUtils.TerrainMapStatusCode UnityEngine.TerrainUtils.TerrainMap::Validate()
extern void TerrainMap_Validate_mAFBB4A2D0290E25D59902A1BD5DA1EBC2ACD1326 (void);
// 0x0000002B System.Void UnityEngine.TerrainUtils.TerrainMap/<>c__DisplayClass3_0::.ctor()
extern void U3CU3Ec__DisplayClass3_0__ctor_mAFD4AEF760F5CC7CE66BAD750DAD3697397E8945 (void);
// 0x0000002C System.Boolean UnityEngine.TerrainUtils.TerrainMap/<>c__DisplayClass3_0::<CreateFromPlacement>b__0(UnityEngine.Terrain)
extern void U3CU3Ec__DisplayClass3_0_U3CCreateFromPlacementU3Eb__0_m703A4D4E3D378C9896199B70A89FCDF1A07C737B (void);
// 0x0000002D System.Boolean UnityEngine.TerrainUtils.TerrainUtility::ValidTerrainsExist()
extern void TerrainUtility_ValidTerrainsExist_m0DD08E4CEC739929A9AEBCEA849EDFE79985A207 (void);
// 0x0000002E System.Void UnityEngine.TerrainUtils.TerrainUtility::ClearConnectivity()
extern void TerrainUtility_ClearConnectivity_m7448E42CD3F2941EF02C10DE358778EEAF9B0AA9 (void);
// 0x0000002F System.Collections.Generic.Dictionary`2<System.Int32,UnityEngine.TerrainUtils.TerrainMap> UnityEngine.TerrainUtils.TerrainUtility::CollectTerrains(System.Boolean)
extern void TerrainUtility_CollectTerrains_mDFCA0AFA00FFD16CEC8B4EFA9C55E3B7B6803EC4 (void);
// 0x00000030 System.Void UnityEngine.TerrainUtils.TerrainUtility::AutoConnect()
extern void TerrainUtility_AutoConnect_m3E435D139BE402DC495248EDD1FF2C1E9377A897 (void);
// 0x00000031 System.Void UnityEngine.TerrainUtils.TerrainUtility/<>c__DisplayClass2_0::.ctor()
extern void U3CU3Ec__DisplayClass2_0__ctor_m4C022C4675BA4CFC7E7AAA5692979CDE6CD8E611 (void);
// 0x00000032 System.Void UnityEngine.TerrainUtils.TerrainUtility/<>c__DisplayClass2_1::.ctor()
extern void U3CU3Ec__DisplayClass2_1__ctor_mA329ED5B221AE8787EAEA1124A2A95675FDD1695 (void);
// 0x00000033 System.Boolean UnityEngine.TerrainUtils.TerrainUtility/<>c__DisplayClass2_1::<CollectTerrains>b__0(UnityEngine.Terrain)
extern void U3CU3Ec__DisplayClass2_1_U3CCollectTerrainsU3Eb__0_m57E871EB2399E5FB7DF78B3C9EBFBF152116AC2C (void);
static Il2CppMethodPointer s_methodPointers[51] = 
{
	EmbeddedAttribute__ctor_mBB01735E786E7770A1B2ECAA65D1219B6D039DA5,
	IsReadOnlyAttribute__ctor_mC20B41FF7D952FD1FE27E289E221F34681837870,
	TerrainData_GetBoundaryValue_mA9217CC15BBC958C9F7071B96CE74769EFDC322E,
	TerrainData__ctor_m1B68EB89248D5706C2528F47279812F824E27A2E,
	TerrainData_Internal_Create_m79BF764CFF5F49D17E2BFC8B20F60B4CF70BE4E1,
	TerrainData_get_size_mCD3977F344B9DEBFF61DD537D03FEB9473838DA5,
	TerrainData_get_alphamapLayers_mF8A0A4F157F7C56354C5A6E3FABF9F230F410F69,
	TerrainData_GetAlphamaps_m2DEF5D2068D54BDAE78661483C1FC4936B06EA01,
	TerrainData_Internal_GetAlphamaps_m6891C19FF72B4394D6BAC3B098A3FCAC1FC6BF36,
	TerrainData_get_alphamapResolution_mC5D1C8FF4A5AFFCCFCF1382FED0D1AD46563D6F8,
	TerrainData_GetAlphamapResolutionInternal_m5C312434763B8F0BD8DE760ACF439DFEFAC2F3E5,
	TerrainData_get_Internal_alphamapResolution_mDA8EF6055A2022B3E1E4E6ECBF8DF4387DE8A930,
	TerrainData_get_alphamapWidth_m07E5B04B08E87AC9F66D766B363000F94C8612D4,
	TerrainData_get_alphamapHeight_m4A8273D6E0E3526A31E2669FBAB240353C086AED,
	TerrainData_SetAlphamaps_m2FA8E29ADF4D1B2B8286ED0FE61E0CEB1663E423,
	TerrainData_Internal_SetAlphamaps_m87D35A2FED5E274AEBDF3ADB00A7FA477861E8FD,
	TerrainData_get_users_m0C569F1AD5853CEBF3C572723A1CAD04AC8433C4,
	TerrainData__cctor_m525F8AF6DEDDACF640BD2D24767502121ED6D9B0,
	TerrainData_get_size_Injected_m0F56E87C4D7EDD1D84F038E4AF0F273D328CF661,
	Terrain_get_terrainData_m3B6C1D89471A4E1C60FC19C168DB37A011B924FD,
	Terrain_get_allowAutoConnect_m4E9CB87D130BB118517C5504C8FB4A5CC3AA91D7,
	Terrain_get_groupingID_mE52E78018126A5D00F837081287BE076E7709C24,
	Terrain_SetNeighbors_m2FFA89D199120125D264EF7EE0BC749A35514C1E,
	Terrain_get_activeTerrain_mAE5A7FE933C2C1A57FC9542E9BFA315A413F224E,
	Terrain_get_activeTerrains_mB90A9BC89764F626D13F3EF1420EA8D3E186B701,
	Terrain__ctor_m11F03EC6C1E68752DDCAE8EF2DED99CFD939FCDC,
	TerrainCallbacks_InvokeHeightmapChangedCallback_m731ED939CBD563CCCE503062602DF5908205AD04,
	TerrainCallbacks_InvokeTextureChangedCallback_mB508E8B7A884854AA01AE5B88AB33E1AE40F4318,
	HeightmapChangedCallback__ctor_m6A7E4189E0A7A1B70EE73818B93B0FC9F613648C,
	HeightmapChangedCallback_Invoke_m63C1C93709641DBE02DCE9F71B7895C5793AF875,
	TextureChangedCallback__ctor_m64076D799FEB79E3D6BE2C4EB33CD081A398F0EF,
	TextureChangedCallback_Invoke_m1194A44102843272B51A70C302EBDBC8214647DE,
	TerrainTileCoord__ctor_m6B6744655B9C3BA9B1A92076F07002B4B4EB899A,
	TerrainMap_GetTerrain_mFF9C935F05859DF70E95994E727565BD67CDD6FC,
	TerrainMap_CreateFromPlacement_mC7822A5F4FC2A2CB119259A48F19D364ACEC5AE7,
	TerrainMap_CreateFromPlacement_m64B90ADBC1D3A1AE18CEC7D0B452377E10B2BCB5,
	TerrainMap_get_terrainTiles_m9EAA8FCB972C834E2093DDD49B26DBBA2E74A2AB,
	TerrainMap__ctor_mCDB47BA50D9D54E65754028F9CF8F91828FE616F,
	TerrainMap_AddTerrainInternal_m507CE3A3F880B33CA2330F69464E3511D5B9BD71,
	TerrainMap_TryToAddTerrain_m03A05C883F317FD2E6956ADD6625409E8A90BE15,
	TerrainMap_ValidateTerrain_m8D9B035B3851E0ED8BB5877BD11F63BA85029653,
	TerrainMap_Validate_mAFBB4A2D0290E25D59902A1BD5DA1EBC2ACD1326,
	U3CU3Ec__DisplayClass3_0__ctor_mAFD4AEF760F5CC7CE66BAD750DAD3697397E8945,
	U3CU3Ec__DisplayClass3_0_U3CCreateFromPlacementU3Eb__0_m703A4D4E3D378C9896199B70A89FCDF1A07C737B,
	TerrainUtility_ValidTerrainsExist_m0DD08E4CEC739929A9AEBCEA849EDFE79985A207,
	TerrainUtility_ClearConnectivity_m7448E42CD3F2941EF02C10DE358778EEAF9B0AA9,
	TerrainUtility_CollectTerrains_mDFCA0AFA00FFD16CEC8B4EFA9C55E3B7B6803EC4,
	TerrainUtility_AutoConnect_m3E435D139BE402DC495248EDD1FF2C1E9377A897,
	U3CU3Ec__DisplayClass2_0__ctor_m4C022C4675BA4CFC7E7AAA5692979CDE6CD8E611,
	U3CU3Ec__DisplayClass2_1__ctor_mA329ED5B221AE8787EAEA1124A2A95675FDD1695,
	U3CU3Ec__DisplayClass2_1_U3CCollectTerrainsU3Eb__0_m57E871EB2399E5FB7DF78B3C9EBFBF152116AC2C,
};
extern void TerrainTileCoord__ctor_m6B6744655B9C3BA9B1A92076F07002B4B4EB899A_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[1] = 
{
	{ 0x06000021, TerrainTileCoord__ctor_m6B6744655B9C3BA9B1A92076F07002B4B4EB899A_AdjustorThunk },
};
static const int32_t s_InvokerIndices[51] = 
{
	6361,
	6361,
	9128,
	6361,
	9443,
	6352,
	6209,
	854,
	854,
	6209,
	6290,
	6209,
	6209,
	6209,
	1457,
	450,
	6235,
	9638,
	4958,
	6235,
	6139,
	6209,
	1039,
	9591,
	9591,
	6361,
	8186,
	7638,
	2898,
	1534,
	2898,
	1042,
	2622,
	2227,
	7931,
	7514,
	6235,
	6361,
	1457,
	1116,
	2622,
	6209,
	6361,
	3616,
	9557,
	9638,
	9213,
	9638,
	6361,
	6361,
	3616,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TerrainModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TerrainModule_CodeGenModule = 
{
	"UnityEngine.TerrainModule.dll",
	51,
	s_methodPointers,
	1,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
