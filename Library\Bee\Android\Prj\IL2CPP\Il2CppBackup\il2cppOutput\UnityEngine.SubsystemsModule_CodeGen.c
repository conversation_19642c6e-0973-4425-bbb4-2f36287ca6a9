﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void UnityEngine.SubsystemManager::ReloadSubsystemsStarted()
extern void SubsystemManager_ReloadSubsystemsStarted_mE6BE70BD76D9C294AFC4ABCD9E4A0DF9BB0E4273 (void);
// 0x00000002 System.Void UnityEngine.SubsystemManager::ReloadSubsystemsCompleted()
extern void SubsystemManager_ReloadSubsystemsCompleted_m708F6B5CEF21605E49DF6DF6DDA81FE3DA19099B (void);
// 0x00000003 System.Void UnityEngine.SubsystemManager::InitializeIntegratedSubsystem(System.IntPtr,UnityEngine.IntegratedSubsystem)
extern void SubsystemManager_InitializeIntegratedSubsystem_mE3336CB0547C0B2CF195BA2372AFBC99F65AB6F5 (void);
// 0x00000004 System.Void UnityEngine.SubsystemManager::ClearSubsystems()
extern void SubsystemManager_ClearSubsystems_m36DD2EBBD868BD00A34CAAFD48BFA4E79D031443 (void);
// 0x00000005 System.Void UnityEngine.SubsystemManager::StaticConstructScriptingClassMap()
extern void SubsystemManager_StaticConstructScriptingClassMap_m350147EEDFABFFADB6541B807F1A94337F8E2337 (void);
// 0x00000006 System.Void UnityEngine.SubsystemManager::.cctor()
extern void SubsystemManager__cctor_m4B044EB8875B225C565E6FB10A2296C0E72BDE2F (void);
// 0x00000007 UnityEngine.IntegratedSubsystem UnityEngine.SubsystemManager::GetIntegratedSubsystemByPtr(System.IntPtr)
extern void SubsystemManager_GetIntegratedSubsystemByPtr_mC117FBE03DF764DB78D48B11AD5AC324F223BDE7 (void);
// 0x00000008 System.Void UnityEngine.IntegratedSubsystem`1::.ctor()
// 0x00000009 System.String UnityEngine.IntegratedSubsystemDescriptor::get_id()
extern void IntegratedSubsystemDescriptor_get_id_m89DBA940C79ED7EFE1137E3EC4A5A53BF7052F15 (void);
// 0x0000000A System.Void UnityEngine.IntegratedSubsystemDescriptor::.ctor()
extern void IntegratedSubsystemDescriptor__ctor_mD10D471BEAB8890C14BF59828EE3C37BCC1538D6 (void);
// 0x0000000B System.Void UnityEngine.IntegratedSubsystemDescriptor`1::.ctor()
// 0x0000000C System.String UnityEngine.SubsystemDescriptorBindings::GetId(System.IntPtr)
extern void SubsystemDescriptorBindings_GetId_mD3D36C3199EF50AAAB7830E6D061269F4CE956C1 (void);
// 0x0000000D System.Void UnityEngine.IntegratedSubsystem::SetHandle(UnityEngine.IntegratedSubsystem)
extern void IntegratedSubsystem_SetHandle_m22D17A2E0BD8AF0FC18CE09638572D34AD8DB48B (void);
// 0x0000000E System.Void UnityEngine.IntegratedSubsystem::.ctor()
extern void IntegratedSubsystem__ctor_m8A0E82CAFC72287502DB0158488C98831B2DF405 (void);
// 0x0000000F System.String UnityEngine.ISubsystemDescriptor::get_id()
// 0x00000010 System.Void UnityEngine.Subsystem::.ctor()
extern void Subsystem__ctor_m43AA875446123D3DCF6016748B19934D0141447A (void);
// 0x00000011 System.String UnityEngine.SubsystemDescriptor::get_id()
extern void SubsystemDescriptor_get_id_mA4223910997DD72DCF70B03BA5340AFE11AA1A01 (void);
// 0x00000012 System.Void UnityEngine.SubsystemDescriptor::.ctor()
extern void SubsystemDescriptor__ctor_m6A1BE1617C93356590A1DE68B9603C83D9A28F05 (void);
// 0x00000013 System.Void UnityEngine.Internal_SubsystemDescriptors::Internal_AddDescriptor(UnityEngine.SubsystemDescriptor)
extern void Internal_SubsystemDescriptors_Internal_AddDescriptor_m0462E74DADC94897AB6EECD3051878FD6832118E (void);
// 0x00000014 System.Void UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::RegisterDeprecatedDescriptor(UnityEngine.SubsystemDescriptor)
extern void SubsystemDescriptorStore_RegisterDeprecatedDescriptor_m0314598F5A51921C9C9B254B4E82C1191FC4D4B1 (void);
// 0x00000015 System.Void UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::InitializeManagedDescriptor(System.IntPtr,UnityEngine.IntegratedSubsystemDescriptor)
extern void SubsystemDescriptorStore_InitializeManagedDescriptor_m74418769D7CDE1CED4F07A179A9173E6F0ECD12C (void);
// 0x00000016 System.Void UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::ClearManagedDescriptors()
extern void SubsystemDescriptorStore_ClearManagedDescriptors_m14AEFE442EDDC8BD082F300C7054FE185E1EA1D5 (void);
// 0x00000017 System.Void UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::ReportSingleSubsystemAnalytics(System.String)
extern void SubsystemDescriptorStore_ReportSingleSubsystemAnalytics_m12986597FBE76C21232348BAC03ADEEB9F02DB99 (void);
// 0x00000018 System.Void UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::RegisterDescriptor(TDescriptor,System.Collections.Generic.List`1<TBaseTypeInList>)
// 0x00000019 System.Void UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::.cctor()
extern void SubsystemDescriptorStore__cctor_mE267CA787BE559BD490D9ED8BD2D55F4E8DE6A14 (void);
// 0x0000001A System.String UnityEngine.SubsystemsImplementation.SubsystemDescriptorWithProvider::get_id()
extern void SubsystemDescriptorWithProvider_get_id_m9E92FDF45FE9BFB0B28C2AE3EFE475998D01BBC7 (void);
// 0x0000001B System.Void UnityEngine.SubsystemsImplementation.SubsystemDescriptorWithProvider::.ctor()
extern void SubsystemDescriptorWithProvider__ctor_mADB008B99F8F98EDD0C5AEBE00368F96D11FCCD1 (void);
// 0x0000001C System.Void UnityEngine.SubsystemsImplementation.SubsystemWithProvider::.ctor()
extern void SubsystemWithProvider__ctor_m7AAC2F13A01D674BF5040F42A08C88611FE60914 (void);
static Il2CppMethodPointer s_methodPointers[28] = 
{
	SubsystemManager_ReloadSubsystemsStarted_mE6BE70BD76D9C294AFC4ABCD9E4A0DF9BB0E4273,
	SubsystemManager_ReloadSubsystemsCompleted_m708F6B5CEF21605E49DF6DF6DDA81FE3DA19099B,
	SubsystemManager_InitializeIntegratedSubsystem_mE3336CB0547C0B2CF195BA2372AFBC99F65AB6F5,
	SubsystemManager_ClearSubsystems_m36DD2EBBD868BD00A34CAAFD48BFA4E79D031443,
	SubsystemManager_StaticConstructScriptingClassMap_m350147EEDFABFFADB6541B807F1A94337F8E2337,
	SubsystemManager__cctor_m4B044EB8875B225C565E6FB10A2296C0E72BDE2F,
	SubsystemManager_GetIntegratedSubsystemByPtr_mC117FBE03DF764DB78D48B11AD5AC324F223BDE7,
	NULL,
	IntegratedSubsystemDescriptor_get_id_m89DBA940C79ED7EFE1137E3EC4A5A53BF7052F15,
	IntegratedSubsystemDescriptor__ctor_mD10D471BEAB8890C14BF59828EE3C37BCC1538D6,
	NULL,
	SubsystemDescriptorBindings_GetId_mD3D36C3199EF50AAAB7830E6D061269F4CE956C1,
	IntegratedSubsystem_SetHandle_m22D17A2E0BD8AF0FC18CE09638572D34AD8DB48B,
	IntegratedSubsystem__ctor_m8A0E82CAFC72287502DB0158488C98831B2DF405,
	NULL,
	Subsystem__ctor_m43AA875446123D3DCF6016748B19934D0141447A,
	SubsystemDescriptor_get_id_mA4223910997DD72DCF70B03BA5340AFE11AA1A01,
	SubsystemDescriptor__ctor_m6A1BE1617C93356590A1DE68B9603C83D9A28F05,
	Internal_SubsystemDescriptors_Internal_AddDescriptor_m0462E74DADC94897AB6EECD3051878FD6832118E,
	SubsystemDescriptorStore_RegisterDeprecatedDescriptor_m0314598F5A51921C9C9B254B4E82C1191FC4D4B1,
	SubsystemDescriptorStore_InitializeManagedDescriptor_m74418769D7CDE1CED4F07A179A9173E6F0ECD12C,
	SubsystemDescriptorStore_ClearManagedDescriptors_m14AEFE442EDDC8BD082F300C7054FE185E1EA1D5,
	SubsystemDescriptorStore_ReportSingleSubsystemAnalytics_m12986597FBE76C21232348BAC03ADEEB9F02DB99,
	NULL,
	SubsystemDescriptorStore__cctor_mE267CA787BE559BD490D9ED8BD2D55F4E8DE6A14,
	SubsystemDescriptorWithProvider_get_id_m9E92FDF45FE9BFB0B28C2AE3EFE475998D01BBC7,
	SubsystemDescriptorWithProvider__ctor_mADB008B99F8F98EDD0C5AEBE00368F96D11FCCD1,
	SubsystemWithProvider__ctor_m7AAC2F13A01D674BF5040F42A08C88611FE60914,
};
static const int32_t s_InvokerIndices[28] = 
{
	9638,
	9638,
	8805,
	9638,
	9638,
	9638,
	9225,
	0,
	6235,
	6361,
	0,
	9225,
	5065,
	6361,
	0,
	6361,
	6235,
	6361,
	9443,
	9443,
	8805,
	9638,
	9443,
	0,
	9638,
	6235,
	6361,
	6361,
};
static const Il2CppTokenRangePair s_rgctxIndices[1] = 
{
	{ 0x06000018, { 0, 9 } },
};
extern const uint32_t g_rgctx_List_1_t4FD62234F80546ED54378254D3B222948841B4AF;
extern const uint32_t g_rgctx_List_1_get_Item_m3935F8C510B45D47FD1F8DFEEE467CE30B145BF4;
extern const uint32_t g_rgctx_TBaseTypeInList_t7042BCACE0A8A2A88132E86C1016AB2EA0E01C54;
extern const Il2CppRGCTXConstrainedData g_rgctx_TBaseTypeInList_t7042BCACE0A8A2A88132E86C1016AB2EA0E01C54_ISubsystemDescriptor_get_id_mB5D2D5D3B9E9FD3ABF5E064E1262DD4AAF09C6F2;
extern const uint32_t g_rgctx_TDescriptor_tD5C1BF59BFFF7F69AC9140BA35E4178DF9C59EC2;
extern const Il2CppRGCTXConstrainedData g_rgctx_TDescriptor_tD5C1BF59BFFF7F69AC9140BA35E4178DF9C59EC2_ISubsystemDescriptor_get_id_mB5D2D5D3B9E9FD3ABF5E064E1262DD4AAF09C6F2;
extern const uint32_t g_rgctx_List_1_set_Item_m58B49CD878D3DC099EB422A63AB014DF5B1CAE06;
extern const uint32_t g_rgctx_List_1_get_Count_mC3BAEE88ADF85DEC0CA10B97B3E7F42183A8AFBB;
extern const uint32_t g_rgctx_List_1_Add_m99E6BCF557EB89898061B9B8761AA6D402DD2592;
static const Il2CppRGCTXDefinition s_rgctxValues[9] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t4FD62234F80546ED54378254D3B222948841B4AF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_m3935F8C510B45D47FD1F8DFEEE467CE30B145BF4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TBaseTypeInList_t7042BCACE0A8A2A88132E86C1016AB2EA0E01C54 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TBaseTypeInList_t7042BCACE0A8A2A88132E86C1016AB2EA0E01C54_ISubsystemDescriptor_get_id_mB5D2D5D3B9E9FD3ABF5E064E1262DD4AAF09C6F2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TDescriptor_tD5C1BF59BFFF7F69AC9140BA35E4178DF9C59EC2 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TDescriptor_tD5C1BF59BFFF7F69AC9140BA35E4178DF9C59EC2_ISubsystemDescriptor_get_id_mB5D2D5D3B9E9FD3ABF5E064E1262DD4AAF09C6F2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_set_Item_m58B49CD878D3DC099EB422A63AB014DF5B1CAE06 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_mC3BAEE88ADF85DEC0CA10B97B3E7F42183A8AFBB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m99E6BCF557EB89898061B9B8761AA6D402DD2592 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_SubsystemsModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_SubsystemsModule_CodeGenModule = 
{
	"UnityEngine.SubsystemsModule.dll",
	28,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	1,
	s_rgctxIndices,
	9,
	s_rgctxValues,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
