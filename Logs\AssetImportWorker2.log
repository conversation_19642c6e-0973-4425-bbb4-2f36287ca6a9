Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
-logFile
Logs/AssetImportWorker2.log
-srvPort
55426
Successfully changed project path to: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Unable to join player connection multicast group (err: 10022).
Unable to join player connection alternative multicast group (err: 10022).
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 161.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56612
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.013004 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 463 ms
Refreshing native plugins compatible for Editor in 118.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.569 seconds
Domain Reload Profiling:
	ReloadAssembly (1570ms)
		BeginReloadAssembly (153ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1250ms)
			LoadAssemblies (150ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (198ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (46ms)
			SetupLoadedEditorAssemblies (941ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (582ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (118ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (165ms)
				ProcessInitializeOnLoadMethodAttributes (74ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.015474 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 122.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.669 seconds
Domain Reload Profiling:
	ReloadAssembly (2670ms)
		BeginReloadAssembly (214ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (29ms)
		EndReloadAssembly (2271ms)
			LoadAssemblies (164ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (400ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (121ms)
			SetupLoadedEditorAssemblies (1552ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (122ms)
				BeforeProcessingInitializeOnLoad (110ms)
				ProcessInitializeOnLoadAttributes (1192ms)
				ProcessInitializeOnLoadMethodAttributes (94ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Refreshing native plugins compatible for Editor in 2.33 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3767 Unused Serialized files (Serialized files now loaded: 0)
Unloading 177 unused Assets / (0.7 MB). Loaded Objects now: 4191.
Memory consumption went from 250.8 MB to 250.0 MB.
Total: 8.408700 ms (FindLiveObjects: 0.467300 ms CreateObjectMapping: 0.259500 ms MarkObjects: 6.868300 ms  DeleteObjects: 0.812400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 3289.261949 seconds.
  path: Assets/texture/Materials/360_F_143908412_PSqF3zrEUekfNlSynAJTnapiC0F0Kw9a.mat
  artifactKey: Guid(c727bf3a47fe09a4894992b6999b4973) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/texture/Materials/360_F_143908412_PSqF3zrEUekfNlSynAJTnapiC0F0Kw9a.mat using Guid(c727bf3a47fe09a4894992b6999b4973) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '839957ac11bd6830fce18fca8dac060e') in 0.416619 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000134 seconds.
  path: Assets/RealisticCarControllerV3/Textures/Mobile Textures/JoystickHandle.png
  artifactKey: Guid(cfedd546bbecccf4582b5ad7aead5800) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Textures/Mobile Textures/JoystickHandle.png using Guid(cfedd546bbecccf4582b5ad7aead5800) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1764897a307d2f54d4b2a45a1d2cbc7a') in 0.206554 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000146 seconds.
  path: Assets/Joystick Pack/Prefabs/Dynamic Joystick.prefab
  artifactKey: Guid(56fae09712773584fb63896d473a98ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Joystick Pack/Prefabs/Dynamic Joystick.prefab using Guid(56fae09712773584fb63896d473a98ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '70a6d0afbc5b6ac5f250f7c42c32ea51') in 0.106532 seconds 
Number of asset objects unloaded after import = 17
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/RealisticCarControllerV3/Prefabs/ChassisJoint.prefab
  artifactKey: Guid(a7e7b1406cded0e4cb52224eb5dc2d80) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Prefabs/ChassisJoint.prefab using Guid(a7e7b1406cded0e4cb52224eb5dc2d80) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f08082f366ec0f5a3c06aba28b3333c4') in 0.041683 seconds 
Number of asset objects unloaded after import = 6
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Joystick Pack/Prefabs/Floating Joystick.prefab
  artifactKey: Guid(0d230cc8be529a542a08cb878ab14b18) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Joystick Pack/Prefabs/Floating Joystick.prefab using Guid(0d230cc8be529a542a08cb878ab14b18) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5f1f04e59d07c35d782c914e609f17cc') in 0.016120 seconds 
Number of asset objects unloaded after import = 17
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0