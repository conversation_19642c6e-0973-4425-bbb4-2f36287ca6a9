﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void UnityEngine.AssetFileNameExtensionAttribute::.ctor(System.String,System.String[])
extern void AssetFileNameExtensionAttribute__ctor_mBDD7C2006F136025094AABE2295D190F43F7F224 (void);
// 0x00000002 System.Void UnityEngine.ThreadAndSerializationSafeAttribute::.ctor()
extern void ThreadAndSerializationSafeAttribute__ctor_m5023D29907E8D1092714DFFA137A8364454709C7 (void);
// 0x00000003 System.Void UnityEngine.WritableAttribute::.ctor()
extern void WritableAttribute__ctor_mC4E14F120A46CC52A65942F34224E2FB20CFF55A (void);
// 0x00000004 System.Void UnityEngine.UnityEngineModuleAssembly::.ctor()
extern void UnityEngineModuleAssembly__ctor_m3F8B023CF6E911C448EF9284C72F7BE92C6E72E3 (void);
// 0x00000005 System.Void UnityEngine.NativeClassAttribute::set_QualifiedNativeName(System.String)
extern void NativeClassAttribute_set_QualifiedNativeName_m5DA8C7AE66E1D4F1ACEA7592ABBF331A2650E0D6 (void);
// 0x00000006 System.Void UnityEngine.NativeClassAttribute::set_Declaration(System.String)
extern void NativeClassAttribute_set_Declaration_mE5497339ED9E55F30DABACBE40AD6D9D36CAE653 (void);
// 0x00000007 System.Void UnityEngine.NativeClassAttribute::.ctor(System.String)
extern void NativeClassAttribute__ctor_mA4C67EDCE7DA70AAADE77FE63EEECFFA911AD0C7 (void);
// 0x00000008 System.Void UnityEngine.NativeClassAttribute::.ctor(System.String,System.String)
extern void NativeClassAttribute__ctor_m92A42152D6ACC3FB9C381EFDFAA691251E2C3DD7 (void);
// 0x00000009 System.String UnityEngine.UnityString::Format(System.String,System.Object[])
extern void UnityString_Format_m98A0629641086A1BE20BBF7F4EADDE3FE3877D85 (void);
// 0x0000000A System.Void UnityEngine.Scripting.UsedByNativeCodeAttribute::.ctor()
extern void UsedByNativeCodeAttribute__ctor_m7C07CF71798D5A94EB849622EA3883E547927D88 (void);
// 0x0000000B System.Void UnityEngine.Scripting.UsedByNativeCodeAttribute::.ctor(System.String)
extern void UsedByNativeCodeAttribute__ctor_m3C75236A1A2C7E364179A7DEDE9E53E7C3CE334D (void);
// 0x0000000C System.Void UnityEngine.Scripting.UsedByNativeCodeAttribute::set_Name(System.String)
extern void UsedByNativeCodeAttribute_set_Name_mF39ED6FE6040AF11CDCAE417EE6FE7DD6BD67E99 (void);
// 0x0000000D System.Void UnityEngine.Scripting.RequiredByNativeCodeAttribute::.ctor()
extern void RequiredByNativeCodeAttribute__ctor_mBCF284DE0C8F84F211ACE616928BE47E29055920 (void);
// 0x0000000E System.Void UnityEngine.Scripting.RequiredByNativeCodeAttribute::.ctor(System.String)
extern void RequiredByNativeCodeAttribute__ctor_m255C8498736E931A7FBB4ABC292F08AE43D3B7BF (void);
// 0x0000000F System.Void UnityEngine.Scripting.RequiredByNativeCodeAttribute::set_Name(System.String)
extern void RequiredByNativeCodeAttribute_set_Name_mF218FB9A613487B6377AF13FA3531E5D581F487B (void);
// 0x00000010 System.Void UnityEngine.Scripting.RequiredByNativeCodeAttribute::set_Optional(System.Boolean)
extern void RequiredByNativeCodeAttribute_set_Optional_m009CBA1D24E7F6EDAE1411CB6A7A01CB2A13B541 (void);
// 0x00000011 System.Void UnityEngine.Scripting.RequiredByNativeCodeAttribute::set_GenerateProxy(System.Boolean)
extern void RequiredByNativeCodeAttribute_set_GenerateProxy_m63E4D29F468D0F254136F1596AEA106BD9EDC89B (void);
// 0x00000012 System.Void UnityEngine.Bindings.VisibleToOtherModulesAttribute::.ctor()
extern void VisibleToOtherModulesAttribute__ctor_m2F00FAC0C9348472A15E93AD256145DCDD967E59 (void);
// 0x00000013 System.Void UnityEngine.Bindings.VisibleToOtherModulesAttribute::.ctor(System.String[])
extern void VisibleToOtherModulesAttribute__ctor_m2FC15A41D7218FFD29ECA4F70323F6DF8F19EC35 (void);
// 0x00000014 System.Void UnityEngine.Bindings.NativeConditionalAttribute::set_Condition(System.String)
extern void NativeConditionalAttribute_set_Condition_m96107E75DC095D9B4A9A7CCE0EB0C3EFAA2F0053 (void);
// 0x00000015 System.Void UnityEngine.Bindings.NativeConditionalAttribute::set_StubReturnStatement(System.String)
extern void NativeConditionalAttribute_set_StubReturnStatement_mAA961429C06F0E4DC9149C26CC092ED29AF5331E (void);
// 0x00000016 System.Void UnityEngine.Bindings.NativeConditionalAttribute::set_Enabled(System.Boolean)
extern void NativeConditionalAttribute_set_Enabled_m379DA383CBBF2539C080D5DC0E8B97F4DB27DA64 (void);
// 0x00000017 System.Void UnityEngine.Bindings.NativeConditionalAttribute::.ctor(System.String)
extern void NativeConditionalAttribute__ctor_mD9C6F4E343C06314DF5DCFDDE9B5495E78F711ED (void);
// 0x00000018 System.Void UnityEngine.Bindings.NativeConditionalAttribute::.ctor(System.String,System.String)
extern void NativeConditionalAttribute__ctor_m5EC9368A4D06044907451D8C77B356E9ADBD241E (void);
// 0x00000019 System.Void UnityEngine.Bindings.NativeHeaderAttribute::set_Header(System.String)
extern void NativeHeaderAttribute_set_Header_mC431D0143381F2B35B08E211C2D5DD011372ADAB (void);
// 0x0000001A System.Void UnityEngine.Bindings.NativeHeaderAttribute::.ctor(System.String)
extern void NativeHeaderAttribute__ctor_mD0D73B93BC695BC42CBF7E7FC6FB044131C3D0BC (void);
// 0x0000001B System.Void UnityEngine.Bindings.NativeNameAttribute::set_Name(System.String)
extern void NativeNameAttribute_set_Name_mA5639D9FDBEADE899CBE13AFA4FCFB61A95ADAE7 (void);
// 0x0000001C System.Void UnityEngine.Bindings.NativeNameAttribute::.ctor(System.String)
extern void NativeNameAttribute__ctor_m9F46C053270D9DBCC9F9AB32C545A7696F0638D0 (void);
// 0x0000001D System.Void UnityEngine.Bindings.NativeWritableSelfAttribute::set_WritableSelf(System.Boolean)
extern void NativeWritableSelfAttribute_set_WritableSelf_mB4B342C1D1678307EB4CF174BEAF8D1E94CDF3E6 (void);
// 0x0000001E System.Void UnityEngine.Bindings.NativeWritableSelfAttribute::.ctor()
extern void NativeWritableSelfAttribute__ctor_mF59616C59BA935E75ED688DCBAF1966036CD039B (void);
// 0x0000001F System.Void UnityEngine.Bindings.NativeMethodAttribute::set_Name(System.String)
extern void NativeMethodAttribute_set_Name_mE223393EB6EEA9E94A8A9CC093CB3CBBCB7C40B8 (void);
// 0x00000020 System.Void UnityEngine.Bindings.NativeMethodAttribute::set_IsThreadSafe(System.Boolean)
extern void NativeMethodAttribute_set_IsThreadSafe_m443623C95A2E3552D0A791DC65E20ADFC447AE3F (void);
// 0x00000021 System.Void UnityEngine.Bindings.NativeMethodAttribute::set_IsFreeFunction(System.Boolean)
extern void NativeMethodAttribute_set_IsFreeFunction_mCF665BA0A4CA25DA0EA8C3C5EDDB9A03315C9C4F (void);
// 0x00000022 System.Void UnityEngine.Bindings.NativeMethodAttribute::set_ThrowsException(System.Boolean)
extern void NativeMethodAttribute_set_ThrowsException_m05A53893F9C6616B40F8F70790C6533C30C64592 (void);
// 0x00000023 System.Void UnityEngine.Bindings.NativeMethodAttribute::set_HasExplicitThis(System.Boolean)
extern void NativeMethodAttribute_set_HasExplicitThis_m41908D1B191AEADF84C548E57A72B4E948D35678 (void);
// 0x00000024 System.Void UnityEngine.Bindings.NativeMethodAttribute::.ctor()
extern void NativeMethodAttribute__ctor_mEA2A3B247A134B4453743CCF55655D16C63C741E (void);
// 0x00000025 System.Void UnityEngine.Bindings.NativeMethodAttribute::.ctor(System.String)
extern void NativeMethodAttribute__ctor_m75590D9A8E1851C1DA619C07522D5D4AA63797B5 (void);
// 0x00000026 System.Void UnityEngine.Bindings.NativeMethodAttribute::.ctor(System.String,System.Boolean)
extern void NativeMethodAttribute__ctor_mCDF45F4290C6955E1E9FE460709D9ACA18B2D06E (void);
// 0x00000027 System.Void UnityEngine.Bindings.NativeMethodAttribute::.ctor(System.String,System.Boolean,System.Boolean)
extern void NativeMethodAttribute__ctor_m0B32894B3BD625410703207C7DBF61098BAEE973 (void);
// 0x00000028 System.Void UnityEngine.Bindings.NativePropertyAttribute::set_TargetType(UnityEngine.Bindings.TargetType)
extern void NativePropertyAttribute_set_TargetType_m858E4E653A2F81F5313EF4F5D69740D945B19ED7 (void);
// 0x00000029 System.Void UnityEngine.Bindings.NativePropertyAttribute::.ctor()
extern void NativePropertyAttribute__ctor_mAF06F05D29FE386561167B2BDF0149E3744411EA (void);
// 0x0000002A System.Void UnityEngine.Bindings.NativePropertyAttribute::.ctor(System.String)
extern void NativePropertyAttribute__ctor_m32EC1C27977EA39BC2824743EC417561AC138BA3 (void);
// 0x0000002B System.Void UnityEngine.Bindings.NativePropertyAttribute::.ctor(System.String,System.Boolean,UnityEngine.Bindings.TargetType)
extern void NativePropertyAttribute__ctor_m61A6A85F47F1111850244DFFD401B94E3234BDC7 (void);
// 0x0000002C System.Void UnityEngine.Bindings.NativePropertyAttribute::.ctor(System.String,System.Boolean,UnityEngine.Bindings.TargetType,System.Boolean)
extern void NativePropertyAttribute__ctor_m6FD4C499DD1D0AD75668B6D860FACB837DFB6095 (void);
// 0x0000002D System.Void UnityEngine.Bindings.NativeAsStructAttribute::.ctor()
extern void NativeAsStructAttribute__ctor_m6AF1CD76481CC41BF7CE5C87DCF6CE68EF187298 (void);
// 0x0000002E System.Void UnityEngine.Bindings.NativeTypeAttribute::set_Header(System.String)
extern void NativeTypeAttribute_set_Header_m847CE2407B2426DFF214F3F18DE9644062A5FB8A (void);
// 0x0000002F System.Void UnityEngine.Bindings.NativeTypeAttribute::set_IntermediateScriptingStructName(System.String)
extern void NativeTypeAttribute_set_IntermediateScriptingStructName_mA5791DC59CA4C7572D8B5C80D28176559E2BC907 (void);
// 0x00000030 System.Void UnityEngine.Bindings.NativeTypeAttribute::set_CodegenOptions(UnityEngine.Bindings.CodegenOptions)
extern void NativeTypeAttribute_set_CodegenOptions_mEECF2309A52F63B6258608EFF56AEAEF5E5572FC (void);
// 0x00000031 System.Void UnityEngine.Bindings.NativeTypeAttribute::.ctor()
extern void NativeTypeAttribute__ctor_mC1B7AC76546C88B8314DBDE006BF54739A8D2BCD (void);
// 0x00000032 System.Void UnityEngine.Bindings.NativeTypeAttribute::.ctor(UnityEngine.Bindings.CodegenOptions)
extern void NativeTypeAttribute__ctor_m42A2C59E33BA5B0DD88A44BA5C9A1C0FDDFBCF46 (void);
// 0x00000033 System.Void UnityEngine.Bindings.NativeTypeAttribute::.ctor(System.String)
extern void NativeTypeAttribute__ctor_m3E053B25317A9630A5D4D9C8730A776A3C8F0487 (void);
// 0x00000034 System.Void UnityEngine.Bindings.NativeTypeAttribute::.ctor(UnityEngine.Bindings.CodegenOptions,System.String)
extern void NativeTypeAttribute__ctor_mAAAA100D0F13ECBDEB655342889B7D82CF920A75 (void);
// 0x00000035 System.Void UnityEngine.Bindings.NotNullAttribute::set_Exception(System.String)
extern void NotNullAttribute_set_Exception_m04F458FD91F138C58DC3A11E7C8E945701ECA528 (void);
// 0x00000036 System.Void UnityEngine.Bindings.NotNullAttribute::.ctor(System.String)
extern void NotNullAttribute__ctor_m739C06B242B13C7519C17D0796F1A8FD18CDB7AA (void);
// 0x00000037 System.Void UnityEngine.Bindings.UnmarshalledAttribute::.ctor()
extern void UnmarshalledAttribute__ctor_m77164A228BF4D50B6CF784E319456DD0A74D0E8A (void);
// 0x00000038 System.Void UnityEngine.Bindings.FreeFunctionAttribute::.ctor()
extern void FreeFunctionAttribute__ctor_m0F9BA14FE9193D3CDE593EBF1EA06531F8400388 (void);
// 0x00000039 System.Void UnityEngine.Bindings.FreeFunctionAttribute::.ctor(System.String)
extern void FreeFunctionAttribute__ctor_m379A417DDF33E9D2BB5227D05DD365584E15DDAC (void);
// 0x0000003A System.Void UnityEngine.Bindings.FreeFunctionAttribute::.ctor(System.String,System.Boolean)
extern void FreeFunctionAttribute__ctor_mAC3C22502820C23E516911E3B620387FB330D9E2 (void);
// 0x0000003B System.Void UnityEngine.Bindings.ThreadSafeAttribute::.ctor()
extern void ThreadSafeAttribute__ctor_m1FFE949649F2986C5526433C39F7BC0413472FA1 (void);
// 0x0000003C System.Void UnityEngine.Bindings.StaticAccessorAttribute::set_Name(System.String)
extern void StaticAccessorAttribute_set_Name_m922EC8831D7A1DDE802D33204B88715EEC7B121B (void);
// 0x0000003D System.Void UnityEngine.Bindings.StaticAccessorAttribute::set_Type(UnityEngine.Bindings.StaticAccessorType)
extern void StaticAccessorAttribute_set_Type_m2B350A872AB9222E3CEC32AA03A6B85E7770BDC0 (void);
// 0x0000003E System.Void UnityEngine.Bindings.StaticAccessorAttribute::.ctor(System.String)
extern void StaticAccessorAttribute__ctor_m441E5A01FEB31C10AE2D65C8F680B650E6CDF5C0 (void);
// 0x0000003F System.Void UnityEngine.Bindings.StaticAccessorAttribute::.ctor(System.String,UnityEngine.Bindings.StaticAccessorType)
extern void StaticAccessorAttribute__ctor_m6E1E237E6E03AC9F4B00C506199F03807BEA33BE (void);
// 0x00000040 System.Void UnityEngine.Bindings.NativeThrowsAttribute::set_ThrowsException(System.Boolean)
extern void NativeThrowsAttribute_set_ThrowsException_m523089D1314C7549B50B3D1123149F064CC0A708 (void);
// 0x00000041 System.Void UnityEngine.Bindings.NativeThrowsAttribute::.ctor()
extern void NativeThrowsAttribute__ctor_m62F4CD2A09BDFD06FD43B74201D68CD2F79E109E (void);
// 0x00000042 System.Void UnityEngine.Bindings.IgnoreAttribute::set_DoesNotContributeToSize(System.Boolean)
extern void IgnoreAttribute_set_DoesNotContributeToSize_m0313EA1B54BF5E9FD61267FD476A7C9FB1A5C439 (void);
// 0x00000043 System.Void UnityEngine.Bindings.IgnoreAttribute::.ctor()
extern void IgnoreAttribute__ctor_mDA2998DACBA21CE6D66510F5AA6C4AB1FC33E323 (void);
// 0x00000044 System.Void UnityEngine.Bindings.PreventReadOnlyInstanceModificationAttribute::.ctor()
extern void PreventReadOnlyInstanceModificationAttribute__ctor_m62694007EB93F071791A5139A34F73695A14B9CC (void);
static Il2CppMethodPointer s_methodPointers[68] = 
{
	AssetFileNameExtensionAttribute__ctor_mBDD7C2006F136025094AABE2295D190F43F7F224,
	ThreadAndSerializationSafeAttribute__ctor_m5023D29907E8D1092714DFFA137A8364454709C7,
	WritableAttribute__ctor_mC4E14F120A46CC52A65942F34224E2FB20CFF55A,
	UnityEngineModuleAssembly__ctor_m3F8B023CF6E911C448EF9284C72F7BE92C6E72E3,
	NativeClassAttribute_set_QualifiedNativeName_m5DA8C7AE66E1D4F1ACEA7592ABBF331A2650E0D6,
	NativeClassAttribute_set_Declaration_mE5497339ED9E55F30DABACBE40AD6D9D36CAE653,
	NativeClassAttribute__ctor_mA4C67EDCE7DA70AAADE77FE63EEECFFA911AD0C7,
	NativeClassAttribute__ctor_m92A42152D6ACC3FB9C381EFDFAA691251E2C3DD7,
	UnityString_Format_m98A0629641086A1BE20BBF7F4EADDE3FE3877D85,
	UsedByNativeCodeAttribute__ctor_m7C07CF71798D5A94EB849622EA3883E547927D88,
	UsedByNativeCodeAttribute__ctor_m3C75236A1A2C7E364179A7DEDE9E53E7C3CE334D,
	UsedByNativeCodeAttribute_set_Name_mF39ED6FE6040AF11CDCAE417EE6FE7DD6BD67E99,
	RequiredByNativeCodeAttribute__ctor_mBCF284DE0C8F84F211ACE616928BE47E29055920,
	RequiredByNativeCodeAttribute__ctor_m255C8498736E931A7FBB4ABC292F08AE43D3B7BF,
	RequiredByNativeCodeAttribute_set_Name_mF218FB9A613487B6377AF13FA3531E5D581F487B,
	RequiredByNativeCodeAttribute_set_Optional_m009CBA1D24E7F6EDAE1411CB6A7A01CB2A13B541,
	RequiredByNativeCodeAttribute_set_GenerateProxy_m63E4D29F468D0F254136F1596AEA106BD9EDC89B,
	VisibleToOtherModulesAttribute__ctor_m2F00FAC0C9348472A15E93AD256145DCDD967E59,
	VisibleToOtherModulesAttribute__ctor_m2FC15A41D7218FFD29ECA4F70323F6DF8F19EC35,
	NativeConditionalAttribute_set_Condition_m96107E75DC095D9B4A9A7CCE0EB0C3EFAA2F0053,
	NativeConditionalAttribute_set_StubReturnStatement_mAA961429C06F0E4DC9149C26CC092ED29AF5331E,
	NativeConditionalAttribute_set_Enabled_m379DA383CBBF2539C080D5DC0E8B97F4DB27DA64,
	NativeConditionalAttribute__ctor_mD9C6F4E343C06314DF5DCFDDE9B5495E78F711ED,
	NativeConditionalAttribute__ctor_m5EC9368A4D06044907451D8C77B356E9ADBD241E,
	NativeHeaderAttribute_set_Header_mC431D0143381F2B35B08E211C2D5DD011372ADAB,
	NativeHeaderAttribute__ctor_mD0D73B93BC695BC42CBF7E7FC6FB044131C3D0BC,
	NativeNameAttribute_set_Name_mA5639D9FDBEADE899CBE13AFA4FCFB61A95ADAE7,
	NativeNameAttribute__ctor_m9F46C053270D9DBCC9F9AB32C545A7696F0638D0,
	NativeWritableSelfAttribute_set_WritableSelf_mB4B342C1D1678307EB4CF174BEAF8D1E94CDF3E6,
	NativeWritableSelfAttribute__ctor_mF59616C59BA935E75ED688DCBAF1966036CD039B,
	NativeMethodAttribute_set_Name_mE223393EB6EEA9E94A8A9CC093CB3CBBCB7C40B8,
	NativeMethodAttribute_set_IsThreadSafe_m443623C95A2E3552D0A791DC65E20ADFC447AE3F,
	NativeMethodAttribute_set_IsFreeFunction_mCF665BA0A4CA25DA0EA8C3C5EDDB9A03315C9C4F,
	NativeMethodAttribute_set_ThrowsException_m05A53893F9C6616B40F8F70790C6533C30C64592,
	NativeMethodAttribute_set_HasExplicitThis_m41908D1B191AEADF84C548E57A72B4E948D35678,
	NativeMethodAttribute__ctor_mEA2A3B247A134B4453743CCF55655D16C63C741E,
	NativeMethodAttribute__ctor_m75590D9A8E1851C1DA619C07522D5D4AA63797B5,
	NativeMethodAttribute__ctor_mCDF45F4290C6955E1E9FE460709D9ACA18B2D06E,
	NativeMethodAttribute__ctor_m0B32894B3BD625410703207C7DBF61098BAEE973,
	NativePropertyAttribute_set_TargetType_m858E4E653A2F81F5313EF4F5D69740D945B19ED7,
	NativePropertyAttribute__ctor_mAF06F05D29FE386561167B2BDF0149E3744411EA,
	NativePropertyAttribute__ctor_m32EC1C27977EA39BC2824743EC417561AC138BA3,
	NativePropertyAttribute__ctor_m61A6A85F47F1111850244DFFD401B94E3234BDC7,
	NativePropertyAttribute__ctor_m6FD4C499DD1D0AD75668B6D860FACB837DFB6095,
	NativeAsStructAttribute__ctor_m6AF1CD76481CC41BF7CE5C87DCF6CE68EF187298,
	NativeTypeAttribute_set_Header_m847CE2407B2426DFF214F3F18DE9644062A5FB8A,
	NativeTypeAttribute_set_IntermediateScriptingStructName_mA5791DC59CA4C7572D8B5C80D28176559E2BC907,
	NativeTypeAttribute_set_CodegenOptions_mEECF2309A52F63B6258608EFF56AEAEF5E5572FC,
	NativeTypeAttribute__ctor_mC1B7AC76546C88B8314DBDE006BF54739A8D2BCD,
	NativeTypeAttribute__ctor_m42A2C59E33BA5B0DD88A44BA5C9A1C0FDDFBCF46,
	NativeTypeAttribute__ctor_m3E053B25317A9630A5D4D9C8730A776A3C8F0487,
	NativeTypeAttribute__ctor_mAAAA100D0F13ECBDEB655342889B7D82CF920A75,
	NotNullAttribute_set_Exception_m04F458FD91F138C58DC3A11E7C8E945701ECA528,
	NotNullAttribute__ctor_m739C06B242B13C7519C17D0796F1A8FD18CDB7AA,
	UnmarshalledAttribute__ctor_m77164A228BF4D50B6CF784E319456DD0A74D0E8A,
	FreeFunctionAttribute__ctor_m0F9BA14FE9193D3CDE593EBF1EA06531F8400388,
	FreeFunctionAttribute__ctor_m379A417DDF33E9D2BB5227D05DD365584E15DDAC,
	FreeFunctionAttribute__ctor_mAC3C22502820C23E516911E3B620387FB330D9E2,
	ThreadSafeAttribute__ctor_m1FFE949649F2986C5526433C39F7BC0413472FA1,
	StaticAccessorAttribute_set_Name_m922EC8831D7A1DDE802D33204B88715EEC7B121B,
	StaticAccessorAttribute_set_Type_m2B350A872AB9222E3CEC32AA03A6B85E7770BDC0,
	StaticAccessorAttribute__ctor_m441E5A01FEB31C10AE2D65C8F680B650E6CDF5C0,
	StaticAccessorAttribute__ctor_m6E1E237E6E03AC9F4B00C506199F03807BEA33BE,
	NativeThrowsAttribute_set_ThrowsException_m523089D1314C7549B50B3D1123149F064CC0A708,
	NativeThrowsAttribute__ctor_m62F4CD2A09BDFD06FD43B74201D68CD2F79E109E,
	IgnoreAttribute_set_DoesNotContributeToSize_m0313EA1B54BF5E9FD61267FD476A7C9FB1A5C439,
	IgnoreAttribute__ctor_mDA2998DACBA21CE6D66510F5AA6C4AB1FC33E323,
	PreventReadOnlyInstanceModificationAttribute__ctor_m62694007EB93F071791A5139A34F73695A14B9CC,
};
static const int32_t s_InvokerIndices[68] = 
{
	2902,
	6361,
	6361,
	6361,
	5065,
	5065,
	5065,
	2902,
	8599,
	6361,
	5065,
	5065,
	6361,
	5065,
	5065,
	4968,
	4968,
	6361,
	5065,
	5065,
	5065,
	4968,
	5065,
	2902,
	5065,
	5065,
	5065,
	5065,
	4968,
	6361,
	5065,
	4968,
	4968,
	4968,
	4968,
	6361,
	5065,
	2886,
	1489,
	5041,
	6361,
	5065,
	1491,
	977,
	6361,
	5065,
	5065,
	5041,
	6361,
	5041,
	5065,
	2644,
	5065,
	5065,
	6361,
	6361,
	5065,
	2886,
	6361,
	5065,
	5041,
	5065,
	2896,
	4968,
	6361,
	4968,
	6361,
	6361,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_SharedInternalsModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_SharedInternalsModule_CodeGenModule = 
{
	"UnityEngine.SharedInternalsModule.dll",
	68,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
