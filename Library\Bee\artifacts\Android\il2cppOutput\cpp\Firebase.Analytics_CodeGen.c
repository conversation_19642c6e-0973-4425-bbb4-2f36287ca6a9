﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"


extern const RuntimeMethod* SWIGExceptionHelper_SetPendingApplicationException_m4937E684176D259F9F0DDDB49A51C7E5EF2A2689_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingArgumentException_m2A871F3A9A9CF2EC436E658A8A588511D87E8472_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingArgumentNullException_m83641808A82547F6BF8D6D2286D8A9EC40C4CAFC_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingArgumentOutOfRangeException_m6F5CD59C2BF8544A10EB93D610B4C02359BFED72_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingArithmeticException_m5C60C270EC9D9D8BBE94305DEEF34790542787AC_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingDivideByZeroException_m462486421E7D016951F2DC4DA9221D883CDA1BD5_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingIOException_mBEFF0726B066D5CBD849F285B0D8380BE999FB14_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingIndexOutOfRangeException_m46EED3094E45A0F2C567F294041C913B4A0BA38D_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingInvalidCastException_m1BD4471332877E6BC3E11595C262566A9ABD06C4_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingInvalidOperationException_m3065F3AC2CE1821CAFA23546704E6BAB34449FC9_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingNullReferenceException_m789A56567632FCDC0A253BC9E3CA0DAC44568D1C_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingOutOfMemoryException_mE1A227D1D30CEBE9863562343AFEB0BCC2B44B2F_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingOverflowException_mCC4ABF49EC27A94568C5D32FB4FDAC9A434D1358_RuntimeMethod_var;
extern const RuntimeMethod* SWIGExceptionHelper_SetPendingSystemException_mE08DD6F21A4BA8A16D1FF2E995E5FC9B1AFE56FA_RuntimeMethod_var;
extern const RuntimeMethod* SWIGStringHelper_CreateString_mF82959F2314ECD2B42B1F2D83B86A0AFEBA507C8_RuntimeMethod_var;



// 0x00000001 System.Void Firebase.Analytics.Parameter::.ctor(System.IntPtr,System.Boolean)
extern void Parameter__ctor_m645D10D57442DF831C9E028821C94B94A0E58FA1 (void);
// 0x00000002 System.Runtime.InteropServices.HandleRef Firebase.Analytics.Parameter::getCPtr(Firebase.Analytics.Parameter)
extern void Parameter_getCPtr_mCE6F6C1ED39D2FC6715C9A483403B018F3613053 (void);
// 0x00000003 System.Void Firebase.Analytics.Parameter::Finalize()
extern void Parameter_Finalize_m65A60722D2B177FFCCD12A2D78091C5748E3BFBB (void);
// 0x00000004 System.Void Firebase.Analytics.Parameter::Dispose()
extern void Parameter_Dispose_mC99B13B274CA4268C94264E6F3B627B3777225A8 (void);
// 0x00000005 System.Void Firebase.Analytics.Parameter::Dispose(System.Boolean)
extern void Parameter_Dispose_m7D9FED432495C01E5C197E377E50D1843080552D (void);
// 0x00000006 System.Void Firebase.Analytics.Parameter::.ctor(System.String,System.String)
extern void Parameter__ctor_m71889DACE276FEFBBA79A6FD792C9B3D70107E7A (void);
// 0x00000007 System.Void Firebase.Analytics.Parameter::.ctor(System.String,System.Int64)
extern void Parameter__ctor_mCD1540E35F4CA90DD66956ACB40B922EEEEBE8B9 (void);
// 0x00000008 System.Void Firebase.Analytics.Parameter::.ctor(System.String,System.Double)
extern void Parameter__ctor_m5925AE5BE26DAA8C98EAD1A67E149571B202DA1E (void);
// 0x00000009 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE::.cctor()
extern void FirebaseAnalyticsPINVOKE__cctor_m87FB410E902EF5394802748921696842BCC41D3D (void);
// 0x0000000A System.IntPtr Firebase.Analytics.FirebaseAnalyticsPINVOKE::new_Parameter__SWIG_0(System.String,System.String)
extern void FirebaseAnalyticsPINVOKE_new_Parameter__SWIG_0_m27BDCC2D980AE8B0F3BB631F4FAA90E5523AE93F (void);
// 0x0000000B System.IntPtr Firebase.Analytics.FirebaseAnalyticsPINVOKE::new_Parameter__SWIG_1(System.String,System.Int64)
extern void FirebaseAnalyticsPINVOKE_new_Parameter__SWIG_1_m0C19399217BFA62AEE10E8AC59706918C9B02FF7 (void);
// 0x0000000C System.IntPtr Firebase.Analytics.FirebaseAnalyticsPINVOKE::new_Parameter__SWIG_2(System.String,System.Double)
extern void FirebaseAnalyticsPINVOKE_new_Parameter__SWIG_2_mDA7C29AE1278EF125D738F42AF1B7A058593013E (void);
// 0x0000000D System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE::delete_Parameter(System.Runtime.InteropServices.HandleRef)
extern void FirebaseAnalyticsPINVOKE_delete_Parameter_mD048E0587B80B58C8F0B7E84509770AC4F126C5B (void);
// 0x0000000E System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE::LogEvent__SWIG_4(System.String)
extern void FirebaseAnalyticsPINVOKE_LogEvent__SWIG_4_m76665A43138046DFCB5FDEE6A6906B09A11216BC (void);
// 0x0000000F System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE::LogEvent__SWIG_5(System.String,System.IntPtr,System.Int32)
extern void FirebaseAnalyticsPINVOKE_LogEvent__SWIG_5_mA16DEE74EB70A05DCD702CEC0681804B4E4D8676 (void);
// 0x00000010 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE::SetUserProperty(System.String,System.String)
extern void FirebaseAnalyticsPINVOKE_SetUserProperty_mCFB11AFE994A63EF9F816EF631EAE32FE57CD9D1 (void);
// 0x00000011 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::SWIGRegisterExceptionCallbacks_FirebaseAnalytics(Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionDelegate,Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionDelegate)
extern void SWIGExceptionHelper_SWIGRegisterExceptionCallbacks_FirebaseAnalytics_mF8715CBD59523BCA3E745ED907B0A110542BCE01 (void);
// 0x00000012 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::SWIGRegisterExceptionCallbacksArgument_FirebaseAnalytics(Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate,Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate,Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate)
extern void SWIGExceptionHelper_SWIGRegisterExceptionCallbacksArgument_FirebaseAnalytics_m64B92800747A1DF0624B22252A0DF5C4450FDF95 (void);
// 0x00000013 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::SetPendingApplicationException(System.String)
extern void SWIGExceptionHelper_SetPendingApplicationException_m4937E684176D259F9F0DDDB49A51C7E5EF2A2689 (void);
// 0x00000014 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::SetPendingArithmeticException(System.String)
extern void SWIGExceptionHelper_SetPendingArithmeticException_m5C60C270EC9D9D8BBE94305DEEF34790542787AC (void);
// 0x00000015 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::SetPendingDivideByZeroException(System.String)
extern void SWIGExceptionHelper_SetPendingDivideByZeroException_m462486421E7D016951F2DC4DA9221D883CDA1BD5 (void);
// 0x00000016 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::SetPendingIndexOutOfRangeException(System.String)
extern void SWIGExceptionHelper_SetPendingIndexOutOfRangeException_m46EED3094E45A0F2C567F294041C913B4A0BA38D (void);
// 0x00000017 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::SetPendingInvalidCastException(System.String)
extern void SWIGExceptionHelper_SetPendingInvalidCastException_m1BD4471332877E6BC3E11595C262566A9ABD06C4 (void);
// 0x00000018 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::SetPendingInvalidOperationException(System.String)
extern void SWIGExceptionHelper_SetPendingInvalidOperationException_m3065F3AC2CE1821CAFA23546704E6BAB34449FC9 (void);
// 0x00000019 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::SetPendingIOException(System.String)
extern void SWIGExceptionHelper_SetPendingIOException_mBEFF0726B066D5CBD849F285B0D8380BE999FB14 (void);
// 0x0000001A System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::SetPendingNullReferenceException(System.String)
extern void SWIGExceptionHelper_SetPendingNullReferenceException_m789A56567632FCDC0A253BC9E3CA0DAC44568D1C (void);
// 0x0000001B System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::SetPendingOutOfMemoryException(System.String)
extern void SWIGExceptionHelper_SetPendingOutOfMemoryException_mE1A227D1D30CEBE9863562343AFEB0BCC2B44B2F (void);
// 0x0000001C System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::SetPendingOverflowException(System.String)
extern void SWIGExceptionHelper_SetPendingOverflowException_mCC4ABF49EC27A94568C5D32FB4FDAC9A434D1358 (void);
// 0x0000001D System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::SetPendingSystemException(System.String)
extern void SWIGExceptionHelper_SetPendingSystemException_mE08DD6F21A4BA8A16D1FF2E995E5FC9B1AFE56FA (void);
// 0x0000001E System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::SetPendingArgumentException(System.String,System.String)
extern void SWIGExceptionHelper_SetPendingArgumentException_m2A871F3A9A9CF2EC436E658A8A588511D87E8472 (void);
// 0x0000001F System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::SetPendingArgumentNullException(System.String,System.String)
extern void SWIGExceptionHelper_SetPendingArgumentNullException_m83641808A82547F6BF8D6D2286D8A9EC40C4CAFC (void);
// 0x00000020 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::SetPendingArgumentOutOfRangeException(System.String,System.String)
extern void SWIGExceptionHelper_SetPendingArgumentOutOfRangeException_m6F5CD59C2BF8544A10EB93D610B4C02359BFED72 (void);
// 0x00000021 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::.cctor()
extern void SWIGExceptionHelper__cctor_mA893DDC4C9889DB01619DC9F9A6CB1B93F832027 (void);
// 0x00000022 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper::.ctor()
extern void SWIGExceptionHelper__ctor_m0977C2538183A07492F33E8A112CF065035D66D1 (void);
// 0x00000023 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionDelegate::.ctor(System.Object,System.IntPtr)
extern void ExceptionDelegate__ctor_mC19FB60B6C5D3DAD0A175B4415682C68755C4DBA (void);
// 0x00000024 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionDelegate::Invoke(System.String)
extern void ExceptionDelegate_Invoke_m04F81A1F411CB5CE0C9E3B773A56840A31A6D45C (void);
// 0x00000025 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate::.ctor(System.Object,System.IntPtr)
extern void ExceptionArgumentDelegate__ctor_m0A03D3E7CBEE3D095D5B2BA83466B053C0C21BBE (void);
// 0x00000026 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGExceptionHelper/ExceptionArgumentDelegate::Invoke(System.String,System.String)
extern void ExceptionArgumentDelegate_Invoke_m1F2056A3878B433B7BBE59429E796F7733A70B0B (void);
// 0x00000027 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGPendingException::Set(System.Exception)
extern void SWIGPendingException_Set_m0D69283A740E826969387F6E718986068375B73E (void);
// 0x00000028 System.Exception Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGPendingException::Retrieve()
extern void SWIGPendingException_Retrieve_m1D44BBBB6046736C0B7AD2F3B3E29D07C8F9A519 (void);
// 0x00000029 System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGPendingException::.cctor()
extern void SWIGPendingException__cctor_mD12B102C40FF515F9234DEAADD323879909F22C3 (void);
// 0x0000002A System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGStringHelper::SWIGRegisterStringCallback_FirebaseAnalytics(Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGStringHelper/SWIGStringDelegate)
extern void SWIGStringHelper_SWIGRegisterStringCallback_FirebaseAnalytics_m4558265C93BD347FD1FD0D5E1AD886C7252627A9 (void);
// 0x0000002B System.String Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGStringHelper::CreateString(System.String)
extern void SWIGStringHelper_CreateString_mF82959F2314ECD2B42B1F2D83B86A0AFEBA507C8 (void);
// 0x0000002C System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGStringHelper::.cctor()
extern void SWIGStringHelper__cctor_m8F9D8E42769ED661BCA623252521BA2459075CB6 (void);
// 0x0000002D System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGStringHelper::.ctor()
extern void SWIGStringHelper__ctor_m3730C9B37CEFF8DA8A55EB601BCE6CA5ABA77D4D (void);
// 0x0000002E System.Void Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGStringHelper/SWIGStringDelegate::.ctor(System.Object,System.IntPtr)
extern void SWIGStringDelegate__ctor_m90DEDB63030E111981EAA108CDE8AEEAE71D4C91 (void);
// 0x0000002F System.String Firebase.Analytics.FirebaseAnalyticsPINVOKE/SWIGStringHelper/SWIGStringDelegate::Invoke(System.String)
extern void SWIGStringDelegate_Invoke_m47EA104E396B4E59322268A79A82694DCCE1E100 (void);
// 0x00000030 System.Void Firebase.Analytics.FirebaseAnalytics::.cctor()
extern void FirebaseAnalytics__cctor_mC245C283209D04E164172DD540ACA071E394B5F0 (void);
// 0x00000031 System.Void Firebase.Analytics.FirebaseAnalytics::LogEvent(System.String)
extern void FirebaseAnalytics_LogEvent_mFEABBB5FA78A17D2BC6D79D44B3710CA93874C37 (void);
// 0x00000032 System.Void Firebase.Analytics.FirebaseAnalytics::LogEvent(System.String,Firebase.Analytics.Parameter[])
extern void FirebaseAnalytics_LogEvent_m27351A3469E8FBC2446717BED1A31193588BC42D (void);
// 0x00000033 System.Void Firebase.Analytics.FirebaseAnalytics::SetUserProperty(System.String,System.String)
extern void FirebaseAnalytics_SetUserProperty_m3F8BDC602CDA035333EA7F3BEBE414C7B15F06FA (void);
static Il2CppMethodPointer s_methodPointers[51] = 
{
	Parameter__ctor_m645D10D57442DF831C9E028821C94B94A0E58FA1,
	Parameter_getCPtr_mCE6F6C1ED39D2FC6715C9A483403B018F3613053,
	Parameter_Finalize_m65A60722D2B177FFCCD12A2D78091C5748E3BFBB,
	Parameter_Dispose_mC99B13B274CA4268C94264E6F3B627B3777225A8,
	Parameter_Dispose_m7D9FED432495C01E5C197E377E50D1843080552D,
	Parameter__ctor_m71889DACE276FEFBBA79A6FD792C9B3D70107E7A,
	Parameter__ctor_mCD1540E35F4CA90DD66956ACB40B922EEEEBE8B9,
	Parameter__ctor_m5925AE5BE26DAA8C98EAD1A67E149571B202DA1E,
	FirebaseAnalyticsPINVOKE__cctor_m87FB410E902EF5394802748921696842BCC41D3D,
	FirebaseAnalyticsPINVOKE_new_Parameter__SWIG_0_m27BDCC2D980AE8B0F3BB631F4FAA90E5523AE93F,
	FirebaseAnalyticsPINVOKE_new_Parameter__SWIG_1_m0C19399217BFA62AEE10E8AC59706918C9B02FF7,
	FirebaseAnalyticsPINVOKE_new_Parameter__SWIG_2_mDA7C29AE1278EF125D738F42AF1B7A058593013E,
	FirebaseAnalyticsPINVOKE_delete_Parameter_mD048E0587B80B58C8F0B7E84509770AC4F126C5B,
	FirebaseAnalyticsPINVOKE_LogEvent__SWIG_4_m76665A43138046DFCB5FDEE6A6906B09A11216BC,
	FirebaseAnalyticsPINVOKE_LogEvent__SWIG_5_mA16DEE74EB70A05DCD702CEC0681804B4E4D8676,
	FirebaseAnalyticsPINVOKE_SetUserProperty_mCFB11AFE994A63EF9F816EF631EAE32FE57CD9D1,
	SWIGExceptionHelper_SWIGRegisterExceptionCallbacks_FirebaseAnalytics_mF8715CBD59523BCA3E745ED907B0A110542BCE01,
	SWIGExceptionHelper_SWIGRegisterExceptionCallbacksArgument_FirebaseAnalytics_m64B92800747A1DF0624B22252A0DF5C4450FDF95,
	SWIGExceptionHelper_SetPendingApplicationException_m4937E684176D259F9F0DDDB49A51C7E5EF2A2689,
	SWIGExceptionHelper_SetPendingArithmeticException_m5C60C270EC9D9D8BBE94305DEEF34790542787AC,
	SWIGExceptionHelper_SetPendingDivideByZeroException_m462486421E7D016951F2DC4DA9221D883CDA1BD5,
	SWIGExceptionHelper_SetPendingIndexOutOfRangeException_m46EED3094E45A0F2C567F294041C913B4A0BA38D,
	SWIGExceptionHelper_SetPendingInvalidCastException_m1BD4471332877E6BC3E11595C262566A9ABD06C4,
	SWIGExceptionHelper_SetPendingInvalidOperationException_m3065F3AC2CE1821CAFA23546704E6BAB34449FC9,
	SWIGExceptionHelper_SetPendingIOException_mBEFF0726B066D5CBD849F285B0D8380BE999FB14,
	SWIGExceptionHelper_SetPendingNullReferenceException_m789A56567632FCDC0A253BC9E3CA0DAC44568D1C,
	SWIGExceptionHelper_SetPendingOutOfMemoryException_mE1A227D1D30CEBE9863562343AFEB0BCC2B44B2F,
	SWIGExceptionHelper_SetPendingOverflowException_mCC4ABF49EC27A94568C5D32FB4FDAC9A434D1358,
	SWIGExceptionHelper_SetPendingSystemException_mE08DD6F21A4BA8A16D1FF2E995E5FC9B1AFE56FA,
	SWIGExceptionHelper_SetPendingArgumentException_m2A871F3A9A9CF2EC436E658A8A588511D87E8472,
	SWIGExceptionHelper_SetPendingArgumentNullException_m83641808A82547F6BF8D6D2286D8A9EC40C4CAFC,
	SWIGExceptionHelper_SetPendingArgumentOutOfRangeException_m6F5CD59C2BF8544A10EB93D610B4C02359BFED72,
	SWIGExceptionHelper__cctor_mA893DDC4C9889DB01619DC9F9A6CB1B93F832027,
	SWIGExceptionHelper__ctor_m0977C2538183A07492F33E8A112CF065035D66D1,
	ExceptionDelegate__ctor_mC19FB60B6C5D3DAD0A175B4415682C68755C4DBA,
	ExceptionDelegate_Invoke_m04F81A1F411CB5CE0C9E3B773A56840A31A6D45C,
	ExceptionArgumentDelegate__ctor_m0A03D3E7CBEE3D095D5B2BA83466B053C0C21BBE,
	ExceptionArgumentDelegate_Invoke_m1F2056A3878B433B7BBE59429E796F7733A70B0B,
	SWIGPendingException_Set_m0D69283A740E826969387F6E718986068375B73E,
	SWIGPendingException_Retrieve_m1D44BBBB6046736C0B7AD2F3B3E29D07C8F9A519,
	SWIGPendingException__cctor_mD12B102C40FF515F9234DEAADD323879909F22C3,
	SWIGStringHelper_SWIGRegisterStringCallback_FirebaseAnalytics_m4558265C93BD347FD1FD0D5E1AD886C7252627A9,
	SWIGStringHelper_CreateString_mF82959F2314ECD2B42B1F2D83B86A0AFEBA507C8,
	SWIGStringHelper__cctor_m8F9D8E42769ED661BCA623252521BA2459075CB6,
	SWIGStringHelper__ctor_m3730C9B37CEFF8DA8A55EB601BCE6CA5ABA77D4D,
	SWIGStringDelegate__ctor_m90DEDB63030E111981EAA108CDE8AEEAE71D4C91,
	SWIGStringDelegate_Invoke_m47EA104E396B4E59322268A79A82694DCCE1E100,
	FirebaseAnalytics__cctor_mC245C283209D04E164172DD540ACA071E394B5F0,
	FirebaseAnalytics_LogEvent_mFEABBB5FA78A17D2BC6D79D44B3710CA93874C37,
	FirebaseAnalytics_LogEvent_m27351A3469E8FBC2446717BED1A31193588BC42D,
	FirebaseAnalytics_SetUserProperty_m3F8BDC602CDA035333EA7F3BEBE414C7B15F06FA,
};
static const int32_t s_InvokerIndices[51] = 
{
	2867,
	9096,
	6361,
	6361,
	4968,
	2902,
	2897,
	2892,
	9638,
	8555,
	8553,
	8552,
	9435,
	9443,
	8172,
	8827,
	6554,
	8180,
	9443,
	9443,
	9443,
	9443,
	9443,
	9443,
	9443,
	9443,
	9443,
	9443,
	9443,
	8827,
	8827,
	8827,
	9638,
	6361,
	2898,
	5065,
	2898,
	2902,
	9443,
	9591,
	9638,
	9443,
	9227,
	9638,
	6361,
	2898,
	4502,
	9638,
	9443,
	8827,
	8827,
};
static const Il2CppTokenIndexMethodTuple s_reversePInvokeIndices[15] = 
{
	{ 0x06000013, 19,  (void**)&SWIGExceptionHelper_SetPendingApplicationException_m4937E684176D259F9F0DDDB49A51C7E5EF2A2689_RuntimeMethod_var, 0 },
	{ 0x06000014, 23,  (void**)&SWIGExceptionHelper_SetPendingArithmeticException_m5C60C270EC9D9D8BBE94305DEEF34790542787AC_RuntimeMethod_var, 0 },
	{ 0x06000015, 24,  (void**)&SWIGExceptionHelper_SetPendingDivideByZeroException_m462486421E7D016951F2DC4DA9221D883CDA1BD5_RuntimeMethod_var, 0 },
	{ 0x06000016, 26,  (void**)&SWIGExceptionHelper_SetPendingIndexOutOfRangeException_m46EED3094E45A0F2C567F294041C913B4A0BA38D_RuntimeMethod_var, 0 },
	{ 0x06000017, 27,  (void**)&SWIGExceptionHelper_SetPendingInvalidCastException_m1BD4471332877E6BC3E11595C262566A9ABD06C4_RuntimeMethod_var, 0 },
	{ 0x06000018, 28,  (void**)&SWIGExceptionHelper_SetPendingInvalidOperationException_m3065F3AC2CE1821CAFA23546704E6BAB34449FC9_RuntimeMethod_var, 0 },
	{ 0x06000019, 25,  (void**)&SWIGExceptionHelper_SetPendingIOException_mBEFF0726B066D5CBD849F285B0D8380BE999FB14_RuntimeMethod_var, 0 },
	{ 0x0600001A, 29,  (void**)&SWIGExceptionHelper_SetPendingNullReferenceException_m789A56567632FCDC0A253BC9E3CA0DAC44568D1C_RuntimeMethod_var, 0 },
	{ 0x0600001B, 30,  (void**)&SWIGExceptionHelper_SetPendingOutOfMemoryException_mE1A227D1D30CEBE9863562343AFEB0BCC2B44B2F_RuntimeMethod_var, 0 },
	{ 0x0600001C, 31,  (void**)&SWIGExceptionHelper_SetPendingOverflowException_mCC4ABF49EC27A94568C5D32FB4FDAC9A434D1358_RuntimeMethod_var, 0 },
	{ 0x0600001D, 32,  (void**)&SWIGExceptionHelper_SetPendingSystemException_mE08DD6F21A4BA8A16D1FF2E995E5FC9B1AFE56FA_RuntimeMethod_var, 0 },
	{ 0x0600001E, 20,  (void**)&SWIGExceptionHelper_SetPendingArgumentException_m2A871F3A9A9CF2EC436E658A8A588511D87E8472_RuntimeMethod_var, 0 },
	{ 0x0600001F, 21,  (void**)&SWIGExceptionHelper_SetPendingArgumentNullException_m83641808A82547F6BF8D6D2286D8A9EC40C4CAFC_RuntimeMethod_var, 0 },
	{ 0x06000020, 22,  (void**)&SWIGExceptionHelper_SetPendingArgumentOutOfRangeException_m6F5CD59C2BF8544A10EB93D610B4C02359BFED72_RuntimeMethod_var, 0 },
	{ 0x0600002B, 33,  (void**)&SWIGStringHelper_CreateString_mF82959F2314ECD2B42B1F2D83B86A0AFEBA507C8_RuntimeMethod_var, 0 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Firebase_Analytics_CodeGenModule;
const Il2CppCodeGenModule g_Firebase_Analytics_CodeGenModule = 
{
	"Firebase.Analytics.dll",
	51,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	15,
	s_reversePInvokeIndices,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
