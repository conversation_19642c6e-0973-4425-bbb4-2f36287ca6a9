Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker5
-projectPath
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
-logFile
Logs/AssetImportWorker5.log
-srvPort
55396
Successfully changed project path to: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Unable to join player connection multicast group (err: 10022).
Unable to join player connection alternative multicast group (err: 10022).
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 153.93 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56472
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.013694 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1212 ms
Refreshing native plugins compatible for Editor in 119.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.315 seconds
Domain Reload Profiling:
	ReloadAssembly (2315ms)
		BeginReloadAssembly (159ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (2ms)
		EndReloadAssembly (1989ms)
			LoadAssemblies (154ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (182ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (50ms)
			SetupLoadedEditorAssemblies (1690ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (1332ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (119ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (165ms)
				ProcessInitializeOnLoadMethodAttributes (71ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.015005 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 118.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.725 seconds
Domain Reload Profiling:
	ReloadAssembly (2726ms)
		BeginReloadAssembly (230ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (35ms)
		EndReloadAssembly (2315ms)
			LoadAssemblies (182ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (392ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (115ms)
			SetupLoadedEditorAssemblies (1603ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (21ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (119ms)
				BeforeProcessingInitializeOnLoad (110ms)
				ProcessInitializeOnLoadAttributes (1244ms)
				ProcessInitializeOnLoadMethodAttributes (95ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.38 seconds
Refreshing native plugins compatible for Editor in 5.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3751 Unused Serialized files (Serialized files now loaded: 0)
Unloading 177 unused Assets / (0.7 MB). Loaded Objects now: 4175.
Memory consumption went from 250.2 MB to 249.5 MB.
Total: 22.622700 ms (FindLiveObjects: 1.264100 ms CreateObjectMapping: 0.340700 ms MarkObjects: 19.208600 ms  DeleteObjects: 1.807100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 26245.509362 seconds.
  path: Assets/Source Assets/Cities/Buildings/Gast Station/Materials/gas_station.mat
  artifactKey: Guid(7d3b18446c4bec442bb8ba24a99a7392) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Source Assets/Cities/Buildings/Gast Station/Materials/gas_station.mat using Guid(7d3b18446c4bec442bb8ba24a99a7392) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8f250a1af490c2209d3f915d7c4fdd64') in 0.322044 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/RealisticCarControllerV3/Lens Flares/FlareStar.psd
  artifactKey: Guid(a61a3220521feec429780c8d9f8e9982) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Lens Flares/FlareStar.psd using Guid(a61a3220521feec429780c8d9f8e9982) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3f2120beba70701fadbca2cf825b36d7') in 0.030957 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Environmnrt/Oil Refinery/Textures/DIESTABT.JPG
  artifactKey: Guid(175ba98ba3ecf2346bd3f56ec18bfbd1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Environmnrt/Oil Refinery/Textures/DIESTABT.JPG using Guid(175ba98ba3ecf2346bd3f56ec18bfbd1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2f86519a001ac5a944d98aa6166957d1') in 0.216274 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018282 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.55 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.719 seconds
Domain Reload Profiling:
	ReloadAssembly (2720ms)
		BeginReloadAssembly (293ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (72ms)
		EndReloadAssembly (2247ms)
			LoadAssemblies (218ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (417ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (92ms)
			SetupLoadedEditorAssemblies (1513ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (116ms)
				ProcessInitializeOnLoadAttributes (1244ms)
				ProcessInitializeOnLoadMethodAttributes (110ms)
				AfterProcessingInitializeOnLoad (15ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 4.70 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3708 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4233.
Memory consumption went from 251.0 MB to 250.3 MB.
Total: 954.005200 ms (FindLiveObjects: 0.941100 ms CreateObjectMapping: 0.376800 ms MarkObjects: 951.443200 ms  DeleteObjects: 1.241800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0