using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;

public class DriverTrigger : MonoBehaviour
{
   public GameObject tractorbutton,driverbutton,tractorcamera,drivercamera,tractorriver,groundriver;
    public Image Fadeimage;
    public float fadeDuration = 0.5f;

    private void OnTriggerEnter(Collider other)
    {
        if (other.CompareTag("Driver"))
        {
            this.transform.GetChild(0).gameObject.SetActive(false);
            this.transform.GetChild(1).gameObject.SetActive(false);
            this.GetComponent<MeshRenderer>().enabled = false;
            this.gameObject.GetComponent<BoxCollider>().enabled = false;
            StartCoroutine(HandleTriggerWithFade());
        }
    }

    IEnumerator HandleTriggerWithFade()
    {
        
        if (Fadeimage != null)
        {
            Fadeimage.gameObject.SetActive(true);
            Fadeimage.DOFade(1f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
        }

        yield return new WaitForSeconds(1.5f);

        tractorbutton.SetActive(true);
        driverbutton.SetActive(false);
        tractorcamera.SetActive(true);
        drivercamera.SetActive(false);
        tractorriver.SetActive(true);
        groundriver.SetActive(false);

        if (Fadeimage != null)
        {
            Fadeimage.DOFade(0f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
            Fadeimage.gameObject.SetActive(false);
            this.gameObject.SetActive(false);
        }
    }
}
