<linker>
	<assembly fullname="Assembly-CSharp">
		<type fullname="AdmobManager/_appOpen" preserve="nothing" serialized="true"/>
		<type fullname="AdmobManager/_bannerAd" preserve="nothing" serialized="true"/>
		<type fullname="AdmobManager/_InterstitialAd" preserve="nothing" serialized="true"/>
		<type fullname="AdmobManager/_RewardedAd" preserve="nothing" serialized="true"/>
		<type fullname="AdmobManager/_RewardedInterstitalAd" preserve="nothing" serialized="true"/>
		<type fullname="AdsController/_loadingAdStuff" preserve="nothing" serialized="true"/>
		<type fullname="Invector.vCharacterController.vThirdPersonMotor/vMovementSpeed" preserve="nothing" serialized="true"/>
		<type fullname="RCC_Camera/CameraTarget" preserve="nothing" serialized="true"/>
		<type fullname="RCC_CarControllerV3/Gear" preserve="nothing" serialized="true"/>
		<type fullname="RCC_ChangableWheels/ChangableWheels" preserve="nothing" serialized="true"/>
		<type fullname="RCC_Damage" preserve="nothing" serialized="true"/>
		<type fullname="RCC_GroundMaterials/GroundMaterialFrictions" preserve="nothing" serialized="true"/>
		<type fullname="RCC_GroundMaterials/TerrainFrictions" preserve="nothing" serialized="true"/>
		<type fullname="RCC_GroundMaterials/TerrainFrictions/SplatmapIndexes" preserve="nothing" serialized="true"/>
		<type fullname="RCC_Inputs" preserve="nothing" serialized="true"/>
		<type fullname="RCC_Settings/BehaviorType" preserve="nothing" serialized="true"/>
		<type fullname="RCC_TruckTrailer/TrailerWheel" preserve="nothing" serialized="true"/>
		<type fullname="WaypointMover/UsedAxis" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="Cinemachine">
		<type fullname="Cinemachine.CinemachineBlendDefinition" preserve="nothing" serialized="true"/>
		<type fullname="Cinemachine.CinemachineBrain/BrainEvent" preserve="nothing" serialized="true"/>
		<type fullname="Cinemachine.CinemachineBrain/VcamActivatedEvent" preserve="nothing" serialized="true"/>
		<type fullname="Cinemachine.CinemachineVirtualCameraBase/TransitionParams" preserve="nothing" serialized="true"/>
		<type fullname="Cinemachine.LensSettings" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="DOTween">
		<type fullname="DG.Tweening.Core.DOTweenSettings/ModulesSetup" preserve="nothing" serialized="true"/>
		<type fullname="DG.Tweening.Core.DOTweenSettings/SafeModeOptions" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="Unity.InputSystem">
		<type fullname="UnityEngine.InputSystem.InputAction" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputActionMap" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputBinding" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputControlScheme" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.InputSystem.InputControlScheme/DeviceRequirement" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="Unity.RenderPipelines.Core.Runtime">
		<type fullname="UnityEngine.Rendering.UI.DebugUIPrefabBundle" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="Unity.TextMeshPro">
		<type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.KerningTable" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_Character" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.VertexGradient" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="Unity.Timeline">
		<type fullname="UnityEngine.Timeline.AudioClipProperties" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Timeline.AudioMixerProperties" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Timeline.MarkerList" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Timeline.TimelineAsset/EditorSettings" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Timeline.TimelineClip" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.CoreModule">
		<type fullname="UnityEngine.Events.ArgumentCache" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Events.PersistentListenerMode" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Events.UnityEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.ExposedReference`1[Cinemachine.CinemachineVirtualCameraBase]" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.RectOffset" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.IMGUIModule">
		<type fullname="UnityEngine.GUISettings" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.GUIStyle" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.TextCoreFontEngineModule">
		<type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.TextCore.Glyph" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.TextCore.GlyphMetrics" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.UI">
		<type fullname="UnityEngine.EventSystems.EventTrigger/Entry" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.EventSystems.EventTrigger/TriggerEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.AnimationTriggers" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Button/ButtonClickedEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.ColorBlock" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Dropdown/DropdownEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Dropdown/OptionData" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Dropdown/OptionDataList" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.FontData" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.InputField/EndEditEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.InputField/OnChangeEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.InputField/SubmitEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Navigation" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Scrollbar/ScrollEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.ScrollRect/ScrollRectEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Slider/SliderEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.SpriteState" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Toggle/ToggleEvent" preserve="nothing" serialized="true"/>
	</assembly>
</linker>
