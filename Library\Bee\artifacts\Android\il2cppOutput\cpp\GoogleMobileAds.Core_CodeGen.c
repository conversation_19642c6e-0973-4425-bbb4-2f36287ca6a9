﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void GoogleMobileAds.Api.AdManager.AdManagerAdRequest::.ctor()
extern void AdManagerAdRequest__ctor_m6ABD41C3112F6CC69BC6212B23A1F21D7E1F3EF7 (void);
// 0x00000002 System.Void GoogleMobileAds.Api.AdRequest::.cctor()
extern void AdRequest__cctor_mB03DE91686BA3B5A8138D7DEA1D46515C904263A (void);
// 0x00000003 System.Void GoogleMobileAds.Api.AdRequest::.ctor()
extern void AdRequest__ctor_mC05DE470CAF8233EFC60787797696650D6CCC3AC (void);
// 0x00000004 System.String GoogleMobileAds.Api.AdRequest::get_Version()
extern void AdRequest_get_Version_mDEA58424E216DCD68C3CD09115A3F0207D8DDF79 (void);
// 0x00000005 System.Void GoogleMobileAds.Api.AdRequest::set_Version(System.String)
extern void AdRequest_set_Version_m75B666E0DD9ED3BA9CAD84DBD487EEC0FB47168F (void);
// 0x00000006 System.String GoogleMobileAds.Api.AdRequest::BuildVersionString(System.String)
extern void AdRequest_BuildVersionString_mE20A7E8289BA107C2409CE7A3DCFEC5770EEC513 (void);
// 0x00000007 System.Void GoogleMobileAds.Api.AdSize::.ctor(System.Int32,System.Int32)
extern void AdSize__ctor_m3135618F57F2ABBC18CD548238DB0078A781884D (void);
// 0x00000008 System.Void GoogleMobileAds.Api.AdSize::.ctor(System.Int32,System.Int32,GoogleMobileAds.Api.AdSize/Type)
extern void AdSize__ctor_mF8EE31157BD7A396CD2ACA8C7973AA9EB3D03D73 (void);
// 0x00000009 GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::CreateAnchoredAdaptiveAdSize(System.Int32,GoogleMobileAds.Api.Orientation)
extern void AdSize_CreateAnchoredAdaptiveAdSize_mEAA9CA664616821D386126D09FF3E451B9719A86 (void);
// 0x0000000A GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::GetCurrentOrientationAnchoredAdaptiveBannerAdSizeWithWidth(System.Int32)
extern void AdSize_GetCurrentOrientationAnchoredAdaptiveBannerAdSizeWithWidth_m47EA294894380833C0500EBA7614840FB15A60EF (void);
// 0x0000000B System.Int32 GoogleMobileAds.Api.AdSize::get_Width()
extern void AdSize_get_Width_m3A441FF13816512B5B9F7E76BCA3C33663801454 (void);
// 0x0000000C System.Int32 GoogleMobileAds.Api.AdSize::get_Height()
extern void AdSize_get_Height_m27BD54A87A022614A731DDE44EBBF21887F79C05 (void);
// 0x0000000D GoogleMobileAds.Api.AdSize/Type GoogleMobileAds.Api.AdSize::get_AdType()
extern void AdSize_get_AdType_mB7503D13611ABD8F59904F20D2BFA2DE1100DF7A (void);
// 0x0000000E GoogleMobileAds.Api.Orientation GoogleMobileAds.Api.AdSize::get_Orientation()
extern void AdSize_get_Orientation_m9F0124F3CB2E79001129E206C089D326B8EF4D61 (void);
// 0x0000000F System.Boolean GoogleMobileAds.Api.AdSize::Equals(System.Object)
extern void AdSize_Equals_m759FC89A81779FE4F570938486706AB55E2A90C0 (void);
// 0x00000010 System.Int32 GoogleMobileAds.Api.AdSize::GetHashCode()
extern void AdSize_GetHashCode_mF8FDC407FD639A12E3372A0C2B593B7E8E300194 (void);
// 0x00000011 System.Void GoogleMobileAds.Api.AdSize::.cctor()
extern void AdSize__cctor_mF64D66ED3C0C6273A589A0BF4767870FAF9AEFC4 (void);
// 0x00000012 System.Void GoogleMobileAds.Api.AdValue::.ctor()
extern void AdValue__ctor_mC8469F36ABE33454B249EBBA51574A1CFAC01010 (void);
// 0x00000013 System.Void GoogleMobileAds.Api.AdValue::set_Precision(GoogleMobileAds.Api.AdValue/PrecisionType)
extern void AdValue_set_Precision_m488B0F9CAEBACDD5013E094660C3DD58B362FD03 (void);
// 0x00000014 System.Void GoogleMobileAds.Api.AdValue::set_Value(System.Int64)
extern void AdValue_set_Value_m7DF76A9D4ED62FE1F5FDBA6FEBBACD4E92115BE9 (void);
// 0x00000015 System.Void GoogleMobileAds.Api.AdValue::set_CurrencyCode(System.String)
extern void AdValue_set_CurrencyCode_mA493466D946C6484F0F20604EF0B013EA0571F71 (void);
// 0x00000016 System.Void GoogleMobileAds.Api.AdapterStatus::.ctor(GoogleMobileAds.Api.AdapterState,System.String,System.Int32)
extern void AdapterStatus__ctor_mB1D229249120A477D0E82FF4CEC24CFEC3860087 (void);
// 0x00000017 System.Void GoogleMobileAds.Api.AdapterStatus::set_InitializationState(GoogleMobileAds.Api.AdapterState)
extern void AdapterStatus_set_InitializationState_mCAC923590AEBF2A82B50D11A840782E926D489FA (void);
// 0x00000018 System.Void GoogleMobileAds.Api.AdapterStatus::set_Description(System.String)
extern void AdapterStatus_set_Description_m8987A66E81FAAC057BEE46A30CB522BA72788F32 (void);
// 0x00000019 System.Void GoogleMobileAds.Api.AdapterStatus::set_Latency(System.Int32)
extern void AdapterStatus_set_Latency_m6AAFC26BBD8DB832E092CA129D496B023B4D2710 (void);
// 0x0000001A System.Void GoogleMobileAds.Api.AdManager.AppEvent::.ctor()
extern void AppEvent__ctor_m0E2E4272BAB77A68C3C1F9415094CA8FCE7911DC (void);
// 0x0000001B System.Void GoogleMobileAds.Api.AdManager.AppEvent::set_Name(System.String)
extern void AppEvent_set_Name_m8E75E505A4D9D98AB77BC7F77A338668CF66670A (void);
// 0x0000001C System.Void GoogleMobileAds.Api.AdManager.AppEvent::set_Data(System.String)
extern void AppEvent_set_Data_m056D5856720FF25B1858884249EAC001A275AB18 (void);
// 0x0000001D System.Void GoogleMobileAds.Api.MaxAdContentRating::.ctor(System.String)
extern void MaxAdContentRating__ctor_mB941F1C15423BA11C1D70F68B0FF19A3C8673856 (void);
// 0x0000001E GoogleMobileAds.Api.MaxAdContentRating GoogleMobileAds.Api.MaxAdContentRating::ToMaxAdContentRating(System.String)
extern void MaxAdContentRating_ToMaxAdContentRating_m9A29CB33E337A74C411FDA07208CB75305880BE5 (void);
// 0x0000001F System.String GoogleMobileAds.Api.MaxAdContentRating::get_Value()
extern void MaxAdContentRating_get_Value_m41F2B893E27FDCE7C4FFF245BD24F1692F0F297E (void);
// 0x00000020 System.Void GoogleMobileAds.Api.MaxAdContentRating::set_Value(System.String)
extern void MaxAdContentRating_set_Value_mED6650BB4FC02D217E2A8A57D6C57634881BD64E (void);
// 0x00000021 System.Void GoogleMobileAds.Api.NativeAdOptions::.ctor()
extern void NativeAdOptions__ctor_mC61B6FE5666A592CD6E4DB7982022BCC5989A256 (void);
// 0x00000022 System.Void GoogleMobileAds.Api.NativeTemplateTextStyle::.ctor()
extern void NativeTemplateTextStyle__ctor_m3D676F98731F5FC4666B0B478220EB6A4A385385 (void);
// 0x00000023 UnityEngine.Color GoogleMobileAds.Api.NativeTemplateTextStyle::get_BackgroundColor()
extern void NativeTemplateTextStyle_get_BackgroundColor_m6345AF83B1934C9D86E839E6644481E408C875C8 (void);
// 0x00000024 UnityEngine.Color GoogleMobileAds.Api.NativeTemplateTextStyle::get_TextColor()
extern void NativeTemplateTextStyle_get_TextColor_mDE19B4E975FFD73496B7FD1AE9F4468C7731EC37 (void);
// 0x00000025 System.Int32 GoogleMobileAds.Api.NativeTemplateTextStyle::get_FontSize()
extern void NativeTemplateTextStyle_get_FontSize_m979C3105F190C4143B90B84595C4778BD50F766B (void);
// 0x00000026 GoogleMobileAds.Api.NativeTemplateFontStyle GoogleMobileAds.Api.NativeTemplateTextStyle::get_Style()
extern void NativeTemplateTextStyle_get_Style_mD15214219D1D414849BFD2E2F32232B9D197473D (void);
// 0x00000027 System.Void GoogleMobileAds.Api.PreloadConfiguration::.ctor()
extern void PreloadConfiguration__ctor_m3B75A7748826ED1128BE6B8E86E8B688E1C6A340 (void);
// 0x00000028 System.Void GoogleMobileAds.Api.RequestConfiguration::.ctor()
extern void RequestConfiguration__ctor_m7AB458811E3A7D1EC0E21E8C5017A7168F806500 (void);
// 0x00000029 System.Void GoogleMobileAds.Api.Reward::.ctor()
extern void Reward__ctor_mABD74A86D38CEBCB2C7D2DE193102D39E6BF8800 (void);
// 0x0000002A System.Void GoogleMobileAds.Api.ServerSideVerificationOptions::.ctor()
extern void ServerSideVerificationOptions__ctor_mF59768694F528ABBBA8A2E407DB5A841E3A09582 (void);
// 0x0000002B System.Void GoogleMobileAds.Api.VideoOptions::.ctor()
extern void VideoOptions__ctor_m713F301E2DB5849BC51392B866385DE08C7F2FDE (void);
// 0x0000002C System.Collections.Generic.Dictionary`2<System.String,System.String> GoogleMobileAds.Api.Mediation.MediationExtras::get_Extras()
extern void MediationExtras_get_Extras_m301316E0049B5580869A85F9414C82445BADEB81 (void);
// 0x0000002D System.String GoogleMobileAds.Api.Mediation.MediationExtras::get_AndroidMediationExtraBuilderClassName()
static Il2CppMethodPointer s_methodPointers[45] = 
{
	AdManagerAdRequest__ctor_m6ABD41C3112F6CC69BC6212B23A1F21D7E1F3EF7,
	AdRequest__cctor_mB03DE91686BA3B5A8138D7DEA1D46515C904263A,
	AdRequest__ctor_mC05DE470CAF8233EFC60787797696650D6CCC3AC,
	AdRequest_get_Version_mDEA58424E216DCD68C3CD09115A3F0207D8DDF79,
	AdRequest_set_Version_m75B666E0DD9ED3BA9CAD84DBD487EEC0FB47168F,
	AdRequest_BuildVersionString_mE20A7E8289BA107C2409CE7A3DCFEC5770EEC513,
	AdSize__ctor_m3135618F57F2ABBC18CD548238DB0078A781884D,
	AdSize__ctor_mF8EE31157BD7A396CD2ACA8C7973AA9EB3D03D73,
	AdSize_CreateAnchoredAdaptiveAdSize_mEAA9CA664616821D386126D09FF3E451B9719A86,
	AdSize_GetCurrentOrientationAnchoredAdaptiveBannerAdSizeWithWidth_m47EA294894380833C0500EBA7614840FB15A60EF,
	AdSize_get_Width_m3A441FF13816512B5B9F7E76BCA3C33663801454,
	AdSize_get_Height_m27BD54A87A022614A731DDE44EBBF21887F79C05,
	AdSize_get_AdType_mB7503D13611ABD8F59904F20D2BFA2DE1100DF7A,
	AdSize_get_Orientation_m9F0124F3CB2E79001129E206C089D326B8EF4D61,
	AdSize_Equals_m759FC89A81779FE4F570938486706AB55E2A90C0,
	AdSize_GetHashCode_mF8FDC407FD639A12E3372A0C2B593B7E8E300194,
	AdSize__cctor_mF64D66ED3C0C6273A589A0BF4767870FAF9AEFC4,
	AdValue__ctor_mC8469F36ABE33454B249EBBA51574A1CFAC01010,
	AdValue_set_Precision_m488B0F9CAEBACDD5013E094660C3DD58B362FD03,
	AdValue_set_Value_m7DF76A9D4ED62FE1F5FDBA6FEBBACD4E92115BE9,
	AdValue_set_CurrencyCode_mA493466D946C6484F0F20604EF0B013EA0571F71,
	AdapterStatus__ctor_mB1D229249120A477D0E82FF4CEC24CFEC3860087,
	AdapterStatus_set_InitializationState_mCAC923590AEBF2A82B50D11A840782E926D489FA,
	AdapterStatus_set_Description_m8987A66E81FAAC057BEE46A30CB522BA72788F32,
	AdapterStatus_set_Latency_m6AAFC26BBD8DB832E092CA129D496B023B4D2710,
	AppEvent__ctor_m0E2E4272BAB77A68C3C1F9415094CA8FCE7911DC,
	AppEvent_set_Name_m8E75E505A4D9D98AB77BC7F77A338668CF66670A,
	AppEvent_set_Data_m056D5856720FF25B1858884249EAC001A275AB18,
	MaxAdContentRating__ctor_mB941F1C15423BA11C1D70F68B0FF19A3C8673856,
	MaxAdContentRating_ToMaxAdContentRating_m9A29CB33E337A74C411FDA07208CB75305880BE5,
	MaxAdContentRating_get_Value_m41F2B893E27FDCE7C4FFF245BD24F1692F0F297E,
	MaxAdContentRating_set_Value_mED6650BB4FC02D217E2A8A57D6C57634881BD64E,
	NativeAdOptions__ctor_mC61B6FE5666A592CD6E4DB7982022BCC5989A256,
	NativeTemplateTextStyle__ctor_m3D676F98731F5FC4666B0B478220EB6A4A385385,
	NativeTemplateTextStyle_get_BackgroundColor_m6345AF83B1934C9D86E839E6644481E408C875C8,
	NativeTemplateTextStyle_get_TextColor_mDE19B4E975FFD73496B7FD1AE9F4468C7731EC37,
	NativeTemplateTextStyle_get_FontSize_m979C3105F190C4143B90B84595C4778BD50F766B,
	NativeTemplateTextStyle_get_Style_mD15214219D1D414849BFD2E2F32232B9D197473D,
	PreloadConfiguration__ctor_m3B75A7748826ED1128BE6B8E86E8B688E1C6A340,
	RequestConfiguration__ctor_m7AB458811E3A7D1EC0E21E8C5017A7168F806500,
	Reward__ctor_mABD74A86D38CEBCB2C7D2DE193102D39E6BF8800,
	ServerSideVerificationOptions__ctor_mF59768694F528ABBBA8A2E407DB5A841E3A09582,
	VideoOptions__ctor_m713F301E2DB5849BC51392B866385DE08C7F2FDE,
	MediationExtras_get_Extras_m301316E0049B5580869A85F9414C82445BADEB81,
	NULL,
};
static const int32_t s_InvokerIndices[45] = 
{
	6361,
	9638,
	6361,
	9591,
	9443,
	9227,
	2622,
	1454,
	8581,
	9223,
	6209,
	6209,
	6209,
	6209,
	3616,
	6209,
	9638,
	6361,
	5041,
	5042,
	5065,
	1465,
	5041,
	5065,
	5041,
	6361,
	5065,
	5065,
	5065,
	9227,
	6235,
	5065,
	6361,
	6361,
	6142,
	6142,
	6209,
	6209,
	6361,
	6361,
	6361,
	6361,
	6361,
	6235,
	0,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_GoogleMobileAds_Core_CodeGenModule;
const Il2CppCodeGenModule g_GoogleMobileAds_Core_CodeGenModule = 
{
	"GoogleMobileAds.Core.dll",
	45,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
