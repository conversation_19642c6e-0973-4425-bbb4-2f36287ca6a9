﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void GoogleMobileAds.Common.AdErrorClientEventArgs::.ctor()
extern void AdErrorClientEventArgs__ctor_mE8C76834CB63E41EDC8F533A16BF1ADE185649BA (void);
// 0x00000002 GoogleMobileAds.Common.IAdErrorClient GoogleMobileAds.Common.AdErrorClientEventArgs::get_AdErrorClient()
extern void AdErrorClientEventArgs_get_AdErrorClient_mCF46AD61FF8A16FF1EEFE8372CBD6F369F6CF9EF (void);
// 0x00000003 System.Void GoogleMobileAds.Common.AdErrorClientEventArgs::set_AdErrorClient(GoogleMobileAds.Common.IAdErrorClient)
extern void AdErrorClientEventArgs_set_AdErrorClient_mF1018FAC40B7908AADE3D91D83CD6AC3310CDC6E (void);
// 0x00000004 System.Void GoogleMobileAds.Common.AdInspectorErrorClientEventArgs::.ctor()
extern void AdInspectorErrorClientEventArgs__ctor_mD589A3C66FE286DEEB9EAF61585447A0AA9C90D2 (void);
// 0x00000005 GoogleMobileAds.Common.IAdInspectorErrorClient GoogleMobileAds.Common.AdInspectorErrorClientEventArgs::get_AdErrorClient()
extern void AdInspectorErrorClientEventArgs_get_AdErrorClient_mD9C13714CC9C1B5EE4A5E7D59AA7EAEF7CEC224B (void);
// 0x00000006 System.Void GoogleMobileAds.Common.AdInspectorErrorClientEventArgs::set_AdErrorClient(GoogleMobileAds.Common.IAdInspectorErrorClient)
extern void AdInspectorErrorClientEventArgs_set_AdErrorClient_m30E0F50965C8A0C01F9CB9C81FE5076079ADB259 (void);
// 0x00000007 System.Void GoogleMobileAds.Common.AppStateEventClient::.ctor()
extern void AppStateEventClient__ctor_m7033F1368759856CC91974B018E4004FC6CB36D6 (void);
// 0x00000008 GoogleMobileAds.Common.AppStateEventClient GoogleMobileAds.Common.AppStateEventClient::get_Instance()
extern void AppStateEventClient_get_Instance_mD6A5472FC082A0EDFB6A26F7290A90E88FEB0E9B (void);
// 0x00000009 System.Void GoogleMobileAds.Common.AppStateEventClient::add_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
extern void AppStateEventClient_add_AppStateChanged_m5F95F6F5F802D911F18C9A0F6F9CEF5C6D41CD7C (void);
// 0x0000000A System.Void GoogleMobileAds.Common.AppStateEventClient::remove_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
extern void AppStateEventClient_remove_AppStateChanged_mCCA2A08AE6710D6FA7AA2418B8CFDC071B189499 (void);
// 0x0000000B System.Void GoogleMobileAds.Common.AppStateEventClient::OnApplicationPause(System.Boolean)
extern void AppStateEventClient_OnApplicationPause_m85DD95B9D3B4E6722106A81E328690F7CAEBA8F8 (void);
// 0x0000000C System.Void GoogleMobileAds.Common.AppStateEventClient::<AppStateChanged>m__0(GoogleMobileAds.Common.AppState)
extern void AppStateEventClient_U3CAppStateChangedU3Em__0_m07E4B12AED15A4F1195CB9A239F6ED3432AE1F67 (void);
// 0x0000000D System.Int32 GoogleMobileAds.Common.IAdErrorClient::GetCode()
// 0x0000000E System.String GoogleMobileAds.Common.IAdErrorClient::GetDomain()
// 0x0000000F System.String GoogleMobileAds.Common.IAdErrorClient::GetMessage()
// 0x00000010 GoogleMobileAds.Common.IAdErrorClient GoogleMobileAds.Common.IAdErrorClient::GetCause()
// 0x00000011 System.Void GoogleMobileAds.Common.IAdManagerBannerClient::add_OnAppEvent(System.Action`1<GoogleMobileAds.Api.AdManager.AppEvent>)
// 0x00000012 System.Void GoogleMobileAds.Common.IAdManagerBannerClient::remove_OnAppEvent(System.Action`1<GoogleMobileAds.Api.AdManager.AppEvent>)
// 0x00000013 System.Collections.Generic.List`1<GoogleMobileAds.Api.AdSize> GoogleMobileAds.Common.IAdManagerBannerClient::get_ValidAdSizes()
// 0x00000014 System.Void GoogleMobileAds.Common.IAdManagerBannerClient::set_ValidAdSizes(System.Collections.Generic.List`1<GoogleMobileAds.Api.AdSize>)
// 0x00000015 System.Void GoogleMobileAds.Common.IAdManagerInterstitialClient::add_OnAppEvent(System.Action`1<GoogleMobileAds.Api.AdManager.AppEvent>)
// 0x00000016 System.Void GoogleMobileAds.Common.IAdManagerInterstitialClient::remove_OnAppEvent(System.Action`1<GoogleMobileAds.Api.AdManager.AppEvent>)
// 0x00000017 GoogleMobileAds.Common.IAdManagerInterstitialClient GoogleMobileAds.Common.IAdManagerInterstitialClient::PollAdManagerAd(System.String)
// 0x00000018 System.String GoogleMobileAds.Common.IAdapterResponseInfoClient::get_AdapterClassName()
// 0x00000019 GoogleMobileAds.Common.IAdErrorClient GoogleMobileAds.Common.IAdapterResponseInfoClient::get_AdError()
// 0x0000001A System.String GoogleMobileAds.Common.IAdapterResponseInfoClient::get_AdSourceId()
// 0x0000001B System.String GoogleMobileAds.Common.IAdapterResponseInfoClient::get_AdSourceName()
// 0x0000001C System.String GoogleMobileAds.Common.IAdapterResponseInfoClient::get_AdSourceInstanceId()
// 0x0000001D System.String GoogleMobileAds.Common.IAdapterResponseInfoClient::get_AdSourceInstanceName()
// 0x0000001E System.Collections.Generic.Dictionary`2<System.String,System.String> GoogleMobileAds.Common.IAdapterResponseInfoClient::get_AdUnitMapping()
// 0x0000001F System.Int64 GoogleMobileAds.Common.IAdapterResponseInfoClient::get_LatencyMillis()
// 0x00000020 System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x00000021 System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x00000022 System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000023 System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000024 System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x00000025 System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x00000026 System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x00000027 System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x00000028 System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000029 System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x0000002A System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x0000002B System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x0000002C System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x0000002D System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x0000002E System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdClicked(System.Action)
// 0x0000002F System.Void GoogleMobileAds.Common.IAppOpenAdClient::remove_OnAdClicked(System.Action)
// 0x00000030 System.Void GoogleMobileAds.Common.IAppOpenAdClient::CreateAppOpenAd()
// 0x00000031 System.Boolean GoogleMobileAds.Common.IAppOpenAdClient::IsAdAvailable(System.String)
// 0x00000032 GoogleMobileAds.Common.IAppOpenAdClient GoogleMobileAds.Common.IAppOpenAdClient::PollAd(System.String)
// 0x00000033 System.Void GoogleMobileAds.Common.IAppOpenAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
// 0x00000034 System.Void GoogleMobileAds.Common.IAppOpenAdClient::Show()
// 0x00000035 System.String GoogleMobileAds.Common.IAppOpenAdClient::GetAdUnitID()
// 0x00000036 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Common.IAppOpenAdClient::GetResponseInfoClient()
// 0x00000037 System.Void GoogleMobileAds.Common.IAppOpenAdClient::DestroyAppOpenAd()
// 0x00000038 System.Void GoogleMobileAds.Common.IAppStateEventClient::add_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
// 0x00000039 System.Void GoogleMobileAds.Common.IAppStateEventClient::remove_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
// 0x0000003A System.Void GoogleMobileAds.Common.IApplicationPreferencesClient::SetInt(System.String,System.Int32)
// 0x0000003B System.Void GoogleMobileAds.Common.IApplicationPreferencesClient::SetString(System.String,System.String)
// 0x0000003C System.Int32 GoogleMobileAds.Common.IApplicationPreferencesClient::GetInt(System.String)
// 0x0000003D System.String GoogleMobileAds.Common.IApplicationPreferencesClient::GetString(System.String)
// 0x0000003E System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x0000003F System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x00000040 System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000041 System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000042 System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdOpening(System.EventHandler`1<System.EventArgs>)
// 0x00000043 System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdOpening(System.EventHandler`1<System.EventArgs>)
// 0x00000044 System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdClosed(System.EventHandler`1<System.EventArgs>)
// 0x00000045 System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdClosed(System.EventHandler`1<System.EventArgs>)
// 0x00000046 System.Void GoogleMobileAds.Common.IBannerClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x00000047 System.Void GoogleMobileAds.Common.IBannerClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x00000048 System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdClicked(System.Action)
// 0x00000049 System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdClicked(System.Action)
// 0x0000004A System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdImpressionRecorded(System.Action)
// 0x0000004B System.Void GoogleMobileAds.Common.IBannerClient::remove_OnAdImpressionRecorded(System.Action)
// 0x0000004C System.Void GoogleMobileAds.Common.IBannerClient::CreateBannerView(System.String,GoogleMobileAds.Api.AdSize,GoogleMobileAds.Api.AdPosition)
// 0x0000004D System.Void GoogleMobileAds.Common.IBannerClient::CreateBannerView(System.String,GoogleMobileAds.Api.AdSize,System.Int32,System.Int32)
// 0x0000004E System.Void GoogleMobileAds.Common.IBannerClient::LoadAd(GoogleMobileAds.Api.AdRequest)
// 0x0000004F System.Void GoogleMobileAds.Common.IBannerClient::ShowBannerView()
// 0x00000050 System.Void GoogleMobileAds.Common.IBannerClient::HideBannerView()
// 0x00000051 System.Void GoogleMobileAds.Common.IBannerClient::DestroyBannerView()
// 0x00000052 System.String GoogleMobileAds.Common.IBannerClient::GetAdUnitID()
// 0x00000053 System.Single GoogleMobileAds.Common.IBannerClient::GetHeightInPixels()
// 0x00000054 System.Single GoogleMobileAds.Common.IBannerClient::GetWidthInPixels()
// 0x00000055 System.Void GoogleMobileAds.Common.IBannerClient::SetPosition(GoogleMobileAds.Api.AdPosition)
// 0x00000056 System.Void GoogleMobileAds.Common.IBannerClient::SetPosition(System.Int32,System.Int32)
// 0x00000057 System.Boolean GoogleMobileAds.Common.IBannerClient::IsCollapsible()
// 0x00000058 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Common.IBannerClient::GetResponseInfoClient()
// 0x00000059 GoogleMobileAds.Common.IAppStateEventClient GoogleMobileAds.IClientFactory::BuildAppStateEventClient()
// 0x0000005A GoogleMobileAds.Common.IAppOpenAdClient GoogleMobileAds.IClientFactory::BuildAppOpenAdClient()
// 0x0000005B GoogleMobileAds.Common.IBannerClient GoogleMobileAds.IClientFactory::BuildBannerClient()
// 0x0000005C GoogleMobileAds.Common.IAdManagerBannerClient GoogleMobileAds.IClientFactory::BuildAdManagerBannerClient()
// 0x0000005D GoogleMobileAds.Common.IInterstitialClient GoogleMobileAds.IClientFactory::BuildInterstitialClient()
// 0x0000005E GoogleMobileAds.Common.IAdManagerInterstitialClient GoogleMobileAds.IClientFactory::BuildAdManagerInterstitialClient()
// 0x0000005F GoogleMobileAds.Common.IRewardedAdClient GoogleMobileAds.IClientFactory::BuildRewardedAdClient()
// 0x00000060 GoogleMobileAds.Common.IRewardedInterstitialAdClient GoogleMobileAds.IClientFactory::BuildRewardedInterstitialAdClient()
// 0x00000061 GoogleMobileAds.Common.INativeOverlayAdClient GoogleMobileAds.IClientFactory::BuildNativeOverlayAdClient()
// 0x00000062 GoogleMobileAds.Common.IApplicationPreferencesClient GoogleMobileAds.IClientFactory::ApplicationPreferencesInstance()
// 0x00000063 GoogleMobileAds.Common.IMobileAdsClient GoogleMobileAds.IClientFactory::MobileAdsInstance()
// 0x00000064 GoogleMobileAds.Api.AdapterStatus GoogleMobileAds.Common.IInitializationStatusClient::getAdapterStatusForClassName(System.String)
// 0x00000065 System.Collections.Generic.Dictionary`2<System.String,GoogleMobileAds.Api.AdapterStatus> GoogleMobileAds.Common.IInitializationStatusClient::getAdapterStatusMap()
// 0x00000066 System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x00000067 System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x00000068 System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000069 System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x0000006A System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x0000006B System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x0000006C System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x0000006D System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x0000006E System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x0000006F System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000070 System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000071 System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000072 System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x00000073 System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x00000074 System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdClicked(System.Action)
// 0x00000075 System.Void GoogleMobileAds.Common.IInterstitialClient::remove_OnAdClicked(System.Action)
// 0x00000076 System.Void GoogleMobileAds.Common.IInterstitialClient::CreateInterstitialAd()
// 0x00000077 System.Boolean GoogleMobileAds.Common.IInterstitialClient::IsAdAvailable(System.String)
// 0x00000078 GoogleMobileAds.Common.IInterstitialClient GoogleMobileAds.Common.IInterstitialClient::PollAd(System.String)
// 0x00000079 System.Void GoogleMobileAds.Common.IInterstitialClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
// 0x0000007A System.Void GoogleMobileAds.Common.IInterstitialClient::Show()
// 0x0000007B System.String GoogleMobileAds.Common.IInterstitialClient::GetAdUnitID()
// 0x0000007C GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Common.IInterstitialClient::GetResponseInfoClient()
// 0x0000007D System.Void GoogleMobileAds.Common.IInterstitialClient::DestroyInterstitial()
// 0x0000007E GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Common.ILoadAdErrorClient::GetResponseInfoClient()
// 0x0000007F System.Void GoogleMobileAds.Common.IMobileAdsClient::Initialize(System.Action`1<GoogleMobileAds.Common.IInitializationStatusClient>)
// 0x00000080 System.Void GoogleMobileAds.Common.IMobileAdsClient::Preload(System.Collections.Generic.List`1<GoogleMobileAds.Api.PreloadConfiguration>,System.Action`1<GoogleMobileAds.Api.PreloadConfiguration>,System.Action`1<GoogleMobileAds.Api.PreloadConfiguration>)
// 0x00000081 System.Void GoogleMobileAds.Common.IMobileAdsClient::DisableMediationInitialization()
// 0x00000082 System.Void GoogleMobileAds.Common.IMobileAdsClient::SetApplicationVolume(System.Single)
// 0x00000083 System.Void GoogleMobileAds.Common.IMobileAdsClient::SetApplicationMuted(System.Boolean)
// 0x00000084 System.Void GoogleMobileAds.Common.IMobileAdsClient::SetiOSAppPauseOnBackground(System.Boolean)
// 0x00000085 System.Void GoogleMobileAds.Common.IMobileAdsClient::DisableSDKCrashReporting()
// 0x00000086 System.Single GoogleMobileAds.Common.IMobileAdsClient::GetDeviceScale()
// 0x00000087 System.Int32 GoogleMobileAds.Common.IMobileAdsClient::GetDeviceSafeWidth()
// 0x00000088 System.Void GoogleMobileAds.Common.IMobileAdsClient::SetRequestConfiguration(GoogleMobileAds.Api.RequestConfiguration)
// 0x00000089 System.Version GoogleMobileAds.Common.IMobileAdsClient::GetSDKVersion()
// 0x0000008A GoogleMobileAds.Api.RequestConfiguration GoogleMobileAds.Common.IMobileAdsClient::GetRequestConfiguration()
// 0x0000008B System.Void GoogleMobileAds.Common.IMobileAdsClient::OpenAdInspector(System.Action`1<GoogleMobileAds.Common.AdInspectorErrorClientEventArgs>)
// 0x0000008C System.Void GoogleMobileAds.Common.INativeOverlayAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x0000008D System.Void GoogleMobileAds.Common.INativeOverlayAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x0000008E System.Void GoogleMobileAds.Common.INativeOverlayAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x0000008F System.Void GoogleMobileAds.Common.INativeOverlayAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x00000090 System.Void GoogleMobileAds.Common.INativeOverlayAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x00000091 System.Void GoogleMobileAds.Common.INativeOverlayAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x00000092 System.Void GoogleMobileAds.Common.INativeOverlayAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000093 System.Void GoogleMobileAds.Common.INativeOverlayAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000094 System.Void GoogleMobileAds.Common.INativeOverlayAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000095 System.Void GoogleMobileAds.Common.INativeOverlayAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x00000096 System.Void GoogleMobileAds.Common.INativeOverlayAdClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x00000097 System.Void GoogleMobileAds.Common.INativeOverlayAdClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x00000098 System.Void GoogleMobileAds.Common.INativeOverlayAdClient::add_OnAdClicked(System.Action)
// 0x00000099 System.Void GoogleMobileAds.Common.INativeOverlayAdClient::remove_OnAdClicked(System.Action)
// 0x0000009A System.Void GoogleMobileAds.Common.INativeOverlayAdClient::Load(System.String,GoogleMobileAds.Api.AdRequest,GoogleMobileAds.Api.NativeAdOptions)
// 0x0000009B System.Void GoogleMobileAds.Common.INativeOverlayAdClient::Render(GoogleMobileAds.Api.NativeTemplateStyle,GoogleMobileAds.Api.AdSize,GoogleMobileAds.Api.AdPosition)
// 0x0000009C System.Void GoogleMobileAds.Common.INativeOverlayAdClient::Render(GoogleMobileAds.Api.NativeTemplateStyle,GoogleMobileAds.Api.AdSize,System.Int32,System.Int32)
// 0x0000009D System.Void GoogleMobileAds.Common.INativeOverlayAdClient::Render(GoogleMobileAds.Api.NativeTemplateStyle,GoogleMobileAds.Api.AdPosition)
// 0x0000009E System.Void GoogleMobileAds.Common.INativeOverlayAdClient::Render(GoogleMobileAds.Api.NativeTemplateStyle,System.Int32,System.Int32)
// 0x0000009F System.Void GoogleMobileAds.Common.INativeOverlayAdClient::Hide()
// 0x000000A0 System.Void GoogleMobileAds.Common.INativeOverlayAdClient::Show()
// 0x000000A1 System.Void GoogleMobileAds.Common.INativeOverlayAdClient::SetPosition(GoogleMobileAds.Api.AdPosition)
// 0x000000A2 System.Void GoogleMobileAds.Common.INativeOverlayAdClient::SetPosition(System.Int32,System.Int32)
// 0x000000A3 System.Void GoogleMobileAds.Common.INativeOverlayAdClient::DestroyAd()
// 0x000000A4 System.Single GoogleMobileAds.Common.INativeOverlayAdClient::GetHeightInPixels()
// 0x000000A5 System.Single GoogleMobileAds.Common.INativeOverlayAdClient::GetWidthInPixels()
// 0x000000A6 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Common.INativeOverlayAdClient::GetResponseInfoClient()
// 0x000000A7 GoogleMobileAds.Common.IAdapterResponseInfoClient GoogleMobileAds.Common.IResponseInfoClient::GetLoadedAdapterResponseInfo()
// 0x000000A8 System.Collections.Generic.List`1<GoogleMobileAds.Common.IAdapterResponseInfoClient> GoogleMobileAds.Common.IResponseInfoClient::GetAdapterResponses()
// 0x000000A9 System.Collections.Generic.Dictionary`2<System.String,System.String> GoogleMobileAds.Common.IResponseInfoClient::GetResponseExtras()
// 0x000000AA System.String GoogleMobileAds.Common.IResponseInfoClient::GetMediationAdapterClassName()
// 0x000000AB System.String GoogleMobileAds.Common.IResponseInfoClient::GetResponseId()
// 0x000000AC System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x000000AD System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x000000AE System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x000000AF System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x000000B0 System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x000000B1 System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x000000B2 System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
// 0x000000B3 System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
// 0x000000B4 System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x000000B5 System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x000000B6 System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x000000B7 System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x000000B8 System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x000000B9 System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x000000BA System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x000000BB System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x000000BC System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdClicked(System.Action)
// 0x000000BD System.Void GoogleMobileAds.Common.IRewardedAdClient::remove_OnAdClicked(System.Action)
// 0x000000BE System.Void GoogleMobileAds.Common.IRewardedAdClient::CreateRewardedAd()
// 0x000000BF System.Boolean GoogleMobileAds.Common.IRewardedAdClient::IsAdAvailable(System.String)
// 0x000000C0 GoogleMobileAds.Common.IRewardedAdClient GoogleMobileAds.Common.IRewardedAdClient::PollAd(System.String)
// 0x000000C1 System.Void GoogleMobileAds.Common.IRewardedAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
// 0x000000C2 GoogleMobileAds.Api.Reward GoogleMobileAds.Common.IRewardedAdClient::GetRewardItem()
// 0x000000C3 System.Void GoogleMobileAds.Common.IRewardedAdClient::Show()
// 0x000000C4 System.String GoogleMobileAds.Common.IRewardedAdClient::GetAdUnitID()
// 0x000000C5 System.Void GoogleMobileAds.Common.IRewardedAdClient::SetServerSideVerificationOptions(GoogleMobileAds.Api.ServerSideVerificationOptions)
// 0x000000C6 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Common.IRewardedAdClient::GetResponseInfoClient()
// 0x000000C7 System.Void GoogleMobileAds.Common.IRewardedAdClient::DestroyRewardedAd()
// 0x000000C8 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x000000C9 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdLoaded(System.EventHandler`1<System.EventArgs>)
// 0x000000CA System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x000000CB System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>)
// 0x000000CC System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x000000CD System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>)
// 0x000000CE System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
// 0x000000CF System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>)
// 0x000000D0 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x000000D1 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>)
// 0x000000D2 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x000000D3 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x000000D4 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x000000D5 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>)
// 0x000000D6 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x000000D7 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>)
// 0x000000D8 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdClicked(System.Action)
// 0x000000D9 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::remove_OnAdClicked(System.Action)
// 0x000000DA System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::CreateRewardedInterstitialAd()
// 0x000000DB System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest)
// 0x000000DC GoogleMobileAds.Api.Reward GoogleMobileAds.Common.IRewardedInterstitialAdClient::GetRewardItem()
// 0x000000DD System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::Show()
// 0x000000DE System.String GoogleMobileAds.Common.IRewardedInterstitialAdClient::GetAdUnitID()
// 0x000000DF System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::SetServerSideVerificationOptions(GoogleMobileAds.Api.ServerSideVerificationOptions)
// 0x000000E0 GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Common.IRewardedInterstitialAdClient::GetResponseInfoClient()
// 0x000000E1 System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::DestroyRewardedInterstitialAd()
// 0x000000E2 System.Void GoogleMobileAds.Common.LoadAdErrorClientEventArgs::.ctor()
extern void LoadAdErrorClientEventArgs__ctor_m2D09A984965A5EDB0506CDAC47BE1675E80AE0AB (void);
// 0x000000E3 GoogleMobileAds.Common.ILoadAdErrorClient GoogleMobileAds.Common.LoadAdErrorClientEventArgs::get_LoadAdErrorClient()
extern void LoadAdErrorClientEventArgs_get_LoadAdErrorClient_m0D8BE9C6EABF53908FFC893F742D512A063DFA69 (void);
// 0x000000E4 System.Void GoogleMobileAds.Common.LoadAdErrorClientEventArgs::set_LoadAdErrorClient(GoogleMobileAds.Common.ILoadAdErrorClient)
extern void LoadAdErrorClientEventArgs_set_LoadAdErrorClient_m6E865CB1B347CEF0B75F55CE43EE3FDBF8353353 (void);
// 0x000000E5 System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::.ctor()
extern void MobileAdsEventExecutor__ctor_m41D53858D8E26BD68A2C99A50E0A3ABAA1AD3EE4 (void);
// 0x000000E6 System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::Initialize()
extern void MobileAdsEventExecutor_Initialize_m76A4DE8CDB2F00244DF305ACE913CDA65750ADEE (void);
// 0x000000E7 System.Boolean GoogleMobileAds.Common.MobileAdsEventExecutor::IsActive()
extern void MobileAdsEventExecutor_IsActive_m739FAFCE76C28483EAD8FDF0D8004DE3FA8D7002 (void);
// 0x000000E8 System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::Awake()
extern void MobileAdsEventExecutor_Awake_mBED27401298ECAA85C578989510914356138EDAB (void);
// 0x000000E9 System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::ExecuteInUpdate(System.Action)
extern void MobileAdsEventExecutor_ExecuteInUpdate_m4D7D863382B3DFA6F791CBCFD275B5F7EE4BA91E (void);
// 0x000000EA System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::InvokeInUpdate(UnityEngine.Events.UnityEvent)
extern void MobileAdsEventExecutor_InvokeInUpdate_m707D79AE5D859144C46C142B97CA2C20BA53A171 (void);
// 0x000000EB System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::Update()
extern void MobileAdsEventExecutor_Update_mA2FA865A80DE36FC26CAD15783037324C74D5113 (void);
// 0x000000EC System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::OnDisable()
extern void MobileAdsEventExecutor_OnDisable_m23177F7EA326224E1CCB135CC0CF96FE54CAC975 (void);
// 0x000000ED System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::.cctor()
extern void MobileAdsEventExecutor__cctor_m7AD876F64AB941DD9AABAEE8B2A16CA87F09B57E (void);
// 0x000000EE System.Void GoogleMobileAds.Common.MobileAdsEventExecutor/<InvokeInUpdate>c__AnonStorey0::.ctor()
extern void U3CInvokeInUpdateU3Ec__AnonStorey0__ctor_mD6FCF6FC84A141A42B2D40632E39BA8F7028216F (void);
// 0x000000EF System.Void GoogleMobileAds.Common.MobileAdsEventExecutor/<InvokeInUpdate>c__AnonStorey0::<>m__0()
extern void U3CInvokeInUpdateU3Ec__AnonStorey0_U3CU3Em__0_m3F739919615588E573F5D6B77761038D0B6D8807 (void);
// 0x000000F0 System.Void GoogleMobileAds.Common.Utils::.ctor()
extern void Utils__ctor_mDF88E7FFAB0CAF54C41DAA38C3224C7DF0EE2AF7 (void);
// 0x000000F1 System.Void GoogleMobileAds.Common.Utils::CheckInitialization()
extern void Utils_CheckInitialization_m50CAF15EFD340BD2F707E80A93300DF39728A2D5 (void);
// 0x000000F2 UnityEngine.Texture2D GoogleMobileAds.Common.Utils::GetTexture2DFromByteArray(System.Byte[])
extern void Utils_GetTexture2DFromByteArray_mFF22AF02E3324A0AA304B751120AE0BA62D9D333 (void);
static Il2CppMethodPointer s_methodPointers[242] = 
{
	AdErrorClientEventArgs__ctor_mE8C76834CB63E41EDC8F533A16BF1ADE185649BA,
	AdErrorClientEventArgs_get_AdErrorClient_mCF46AD61FF8A16FF1EEFE8372CBD6F369F6CF9EF,
	AdErrorClientEventArgs_set_AdErrorClient_mF1018FAC40B7908AADE3D91D83CD6AC3310CDC6E,
	AdInspectorErrorClientEventArgs__ctor_mD589A3C66FE286DEEB9EAF61585447A0AA9C90D2,
	AdInspectorErrorClientEventArgs_get_AdErrorClient_mD9C13714CC9C1B5EE4A5E7D59AA7EAEF7CEC224B,
	AdInspectorErrorClientEventArgs_set_AdErrorClient_m30E0F50965C8A0C01F9CB9C81FE5076079ADB259,
	AppStateEventClient__ctor_m7033F1368759856CC91974B018E4004FC6CB36D6,
	AppStateEventClient_get_Instance_mD6A5472FC082A0EDFB6A26F7290A90E88FEB0E9B,
	AppStateEventClient_add_AppStateChanged_m5F95F6F5F802D911F18C9A0F6F9CEF5C6D41CD7C,
	AppStateEventClient_remove_AppStateChanged_mCCA2A08AE6710D6FA7AA2418B8CFDC071B189499,
	AppStateEventClient_OnApplicationPause_m85DD95B9D3B4E6722106A81E328690F7CAEBA8F8,
	AppStateEventClient_U3CAppStateChangedU3Em__0_m07E4B12AED15A4F1195CB9A239F6ED3432AE1F67,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	LoadAdErrorClientEventArgs__ctor_m2D09A984965A5EDB0506CDAC47BE1675E80AE0AB,
	LoadAdErrorClientEventArgs_get_LoadAdErrorClient_m0D8BE9C6EABF53908FFC893F742D512A063DFA69,
	LoadAdErrorClientEventArgs_set_LoadAdErrorClient_m6E865CB1B347CEF0B75F55CE43EE3FDBF8353353,
	MobileAdsEventExecutor__ctor_m41D53858D8E26BD68A2C99A50E0A3ABAA1AD3EE4,
	MobileAdsEventExecutor_Initialize_m76A4DE8CDB2F00244DF305ACE913CDA65750ADEE,
	MobileAdsEventExecutor_IsActive_m739FAFCE76C28483EAD8FDF0D8004DE3FA8D7002,
	MobileAdsEventExecutor_Awake_mBED27401298ECAA85C578989510914356138EDAB,
	MobileAdsEventExecutor_ExecuteInUpdate_m4D7D863382B3DFA6F791CBCFD275B5F7EE4BA91E,
	MobileAdsEventExecutor_InvokeInUpdate_m707D79AE5D859144C46C142B97CA2C20BA53A171,
	MobileAdsEventExecutor_Update_mA2FA865A80DE36FC26CAD15783037324C74D5113,
	MobileAdsEventExecutor_OnDisable_m23177F7EA326224E1CCB135CC0CF96FE54CAC975,
	MobileAdsEventExecutor__cctor_m7AD876F64AB941DD9AABAEE8B2A16CA87F09B57E,
	U3CInvokeInUpdateU3Ec__AnonStorey0__ctor_mD6FCF6FC84A141A42B2D40632E39BA8F7028216F,
	U3CInvokeInUpdateU3Ec__AnonStorey0_U3CU3Em__0_m3F739919615588E573F5D6B77761038D0B6D8807,
	Utils__ctor_mDF88E7FFAB0CAF54C41DAA38C3224C7DF0EE2AF7,
	Utils_CheckInitialization_m50CAF15EFD340BD2F707E80A93300DF39728A2D5,
	Utils_GetTexture2DFromByteArray_mFF22AF02E3324A0AA304B751120AE0BA62D9D333,
};
static const int32_t s_InvokerIndices[242] = 
{
	6361,
	6235,
	5065,
	6361,
	6235,
	5065,
	6361,
	9591,
	5065,
	5065,
	4968,
	9439,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6361,
	6235,
	5065,
	6361,
	9638,
	9557,
	6361,
	9443,
	9443,
	6361,
	6361,
	9638,
	6361,
	6361,
	6361,
	9638,
	9227,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_GoogleMobileAds_Common_CodeGenModule;
const Il2CppCodeGenModule g_GoogleMobileAds_Common_CodeGenModule = 
{
	"GoogleMobileAds.Common.dll",
	242,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
