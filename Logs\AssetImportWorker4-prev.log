Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker4
-projectPath
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
-logFile
Logs/AssetImportWorker4.log
-srvPort
55396
Successfully changed project path to: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Unable to join player connection multicast group (err: 10022).
Unable to join player connection alternative multicast group (err: 10022).
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 150.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56108
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.013212 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1243 ms
Refreshing native plugins compatible for Editor in 118.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.358 seconds
Domain Reload Profiling:
	ReloadAssembly (2358ms)
		BeginReloadAssembly (171ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (2ms)
		EndReloadAssembly (2019ms)
			LoadAssemblies (166ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (182ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (47ms)
			SetupLoadedEditorAssemblies (1722ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (1363ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (119ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (167ms)
				ProcessInitializeOnLoadMethodAttributes (70ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.015045 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 116.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.735 seconds
Domain Reload Profiling:
	ReloadAssembly (2736ms)
		BeginReloadAssembly (231ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (36ms)
		EndReloadAssembly (2321ms)
			LoadAssemblies (180ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (403ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (122ms)
			SetupLoadedEditorAssemblies (1592ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (116ms)
				BeforeProcessingInitializeOnLoad (110ms)
				ProcessInitializeOnLoadAttributes (1237ms)
				ProcessInitializeOnLoadMethodAttributes (95ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.47 seconds
Refreshing native plugins compatible for Editor in 6.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3751 Unused Serialized files (Serialized files now loaded: 0)
Unloading 177 unused Assets / (0.7 MB). Loaded Objects now: 4175.
Memory consumption went from 250.0 MB to 249.3 MB.
Total: 46.561900 ms (FindLiveObjects: 1.448200 ms CreateObjectMapping: 0.399900 ms MarkObjects: 43.404500 ms  DeleteObjects: 1.305000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 26245.634747 seconds.
  path: Assets/RealisticCarControllerV3/Sounds/Engine/EngineStart.WAV
  artifactKey: Guid(8d3ddfbc69660b342b48f88b496dedee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Sounds/Engine/EngineStart.WAV using Guid(8d3ddfbc69660b342b48f88b496dedee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c4a32d3edb34e7239938e71f6e4fd6ca') in 0.304350 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Source Assets/Cities/Buildings/Gast Station/Gas Station.prefab
  artifactKey: Guid(6b0961b54121e6f4bae76d17e7c5574a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Source Assets/Cities/Buildings/Gast Station/Gas Station.prefab using Guid(6b0961b54121e6f4bae76d17e7c5574a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd7ad3499412eb6d40d538fc19fe1b6e9') in 0.137985 seconds 
Number of asset objects unloaded after import = 11
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/script/Ui/sample + slicing/slicing/gameplay/buttons/start engine.png
  artifactKey: Guid(5bf2228e73511eb4ba5d32ecb0ee2464) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/script/Ui/sample + slicing/slicing/gameplay/buttons/start engine.png using Guid(5bf2228e73511eb4ba5d32ecb0ee2464) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd9af418db255c7916924f33538600e7a') in 0.124845 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014759 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.724 seconds
Domain Reload Profiling:
	ReloadAssembly (2725ms)
		BeginReloadAssembly (298ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (74ms)
		EndReloadAssembly (2240ms)
			LoadAssemblies (224ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (402ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (93ms)
			SetupLoadedEditorAssemblies (1520ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (2ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (106ms)
				ProcessInitializeOnLoadAttributes (1262ms)
				ProcessInitializeOnLoadMethodAttributes (109ms)
				AfterProcessingInitializeOnLoad (16ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (12ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 4.48 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3708 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4204.
Memory consumption went from 250.6 MB to 249.9 MB.
Total: 797.515900 ms (FindLiveObjects: 0.787500 ms CreateObjectMapping: 0.361400 ms MarkObjects: 792.777000 ms  DeleteObjects: 3.588100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014518 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.55 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.381 seconds
Domain Reload Profiling:
	ReloadAssembly (2382ms)
		BeginReloadAssembly (243ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (1980ms)
			LoadAssemblies (166ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (355ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1357ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1140ms)
				ProcessInitializeOnLoadMethodAttributes (90ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.78 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3704 Unused Serialized files (Serialized files now loaded: 0)
Unloading 162 unused Assets / (0.7 MB). Loaded Objects now: 4215.
Memory consumption went from 250.5 MB to 249.8 MB.
Total: 13.952000 ms (FindLiveObjects: 0.702800 ms CreateObjectMapping: 0.476400 ms MarkObjects: 11.451600 ms  DeleteObjects: 1.319300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 115.593321 seconds.
  path: Assets/Scenes/TPSFREEHAND.unity
  artifactKey: Guid(12a4d75da2db4d3439c09e07c73a85b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/TPSFREEHAND.unity using Guid(12a4d75da2db4d3439c09e07c73a85b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a78ff79868ef319aa5633e650bc63afe') in 0.054612 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 39.651921 seconds.
  path: Assets/EasyStart Third Person Controller
  artifactKey: Guid(c6dd9812cc3fab5429149311030dbe9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/EasyStart Third Person Controller using Guid(c6dd9812cc3fab5429149311030dbe9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd2aa87c4c014ba1b18689108422d89c7') in 0.002598 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016130 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.457 seconds
Domain Reload Profiling:
	ReloadAssembly (2458ms)
		BeginReloadAssembly (243ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (2054ms)
			LoadAssemblies (162ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (375ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (90ms)
			SetupLoadedEditorAssemblies (1389ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1169ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3724 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4250.
Memory consumption went from 251.0 MB to 250.3 MB.
Total: 14.362000 ms (FindLiveObjects: 0.710700 ms CreateObjectMapping: 0.469900 ms MarkObjects: 11.850600 ms  DeleteObjects: 1.328900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015629 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.421 seconds
Domain Reload Profiling:
	ReloadAssembly (2422ms)
		BeginReloadAssembly (237ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (60ms)
		EndReloadAssembly (2019ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (360ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (1380ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (95ms)
				ProcessInitializeOnLoadAttributes (1149ms)
				ProcessInitializeOnLoadMethodAttributes (97ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.53 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3724 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4265.
Memory consumption went from 251.1 MB to 250.4 MB.
Total: 8.491600 ms (FindLiveObjects: 0.499400 ms CreateObjectMapping: 0.262900 ms MarkObjects: 6.785100 ms  DeleteObjects: 0.940900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 401.278935 seconds.
  path: Assets/assets/PopulationSystem/CharacterPack01/Meshes/Materials/Farmer.mat
  artifactKey: Guid(a44aae351a3f83c44acf5bff1e58ef10) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/assets/PopulationSystem/CharacterPack01/Meshes/Materials/Farmer.mat using Guid(a44aae351a3f83c44acf5bff1e58ef10) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4e8510c2a4d1beae07a4b3f0942ea5d2') in 0.140513 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/assets/PopulationSystem/CharacterPack01/Meshes/Materials/Farmer 1.mat
  artifactKey: Guid(bdff6d12cf4feca498d07c226625d8a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/assets/PopulationSystem/CharacterPack01/Meshes/Materials/Farmer 1.mat using Guid(bdff6d12cf4feca498d07c226625d8a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a70968a17d0dc270718a9c85c3efd12d') in 0.024265 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/assets/PopulationSystem/CharacterPack01/Animations/<EMAIL>
  artifactKey: Guid(ece43b1aceb43d842a26fac5c14a080d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/assets/PopulationSystem/CharacterPack01/Animations/<EMAIL> using Guid(ece43b1aceb43d842a26fac5c14a080d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a81020efc5356989770390baee95d51e') in 0.185265 seconds 
Number of asset objects unloaded after import = 116
========================================================================
Received Import Request.
  Time since last request: 3.043737 seconds.
  path: Assets/kachra/kachra/Song game/Jutt #newsong #punjabi #song #music #punjabisong #farming #pent #newmusic #automobile - 128.mp3
  artifactKey: Guid(4941e7c81a669a749a9868af2ecc0f29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/kachra/kachra/Song game/Jutt #newsong #punjabi #song #music #punjabisong #farming #pent #newmusic #automobile - 128.mp3 using Guid(4941e7c81a669a749a9868af2ecc0f29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'eab3bf1fbaaff3bda14dadb75eb3a319') in 0.041057 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 2.812401 seconds.
  path: Assets/Game scene/Farming mod.unity
  artifactKey: Guid(ebb0204f92bb3ab47bfb1a7825f65c1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Game scene/Farming mod.unity using Guid(ebb0204f92bb3ab47bfb1a7825f65c1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e68c6757f71e53de3c22c91268526f7c') in 0.020772 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 35.970104 seconds.
  path: Assets/3RDMODEASSET/driver baba.fbx
  artifactKey: Guid(97b6a00c4d308424385851384ad6b86c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/3RDMODEASSET/driver baba.fbx using Guid(97b6a00c4d308424385851384ad6b86c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '084dbc5741d2ca5aeca89e9349f42314') in 0.616150 seconds 
Number of asset objects unloaded after import = 41
========================================================================
Received Import Request.
  Time since last request: 35.905208 seconds.
  path: Assets/assets/PopulationSystem/CharacterPack01/Meshes/Farmer 2.FBX
  artifactKey: Guid(bcbca6b7ff30ede4ab80d57895589db4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/assets/PopulationSystem/CharacterPack01/Meshes/Farmer 2.FBX using Guid(bcbca6b7ff30ede4ab80d57895589db4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7d8e9a40f972dfd9ffd31a1fe2db08a3') in 0.087378 seconds 
Number of asset objects unloaded after import = 133
========================================================================
Received Import Request.
  Time since last request: 10.413476 seconds.
  path: Assets/assets/PopulationSystem/CharacterPack01/Meshes/Farmer Tractor.FBX
  artifactKey: Guid(bcbca6b7ff30ede4ab80d57895589db4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/assets/PopulationSystem/CharacterPack01/Meshes/Farmer Tractor.FBX using Guid(bcbca6b7ff30ede4ab80d57895589db4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0dfbca36797f5cbd842a9cacff162f48') in 0.087070 seconds 
Number of asset objects unloaded after import = 133
========================================================================
Received Import Request.
  Time since last request: 3.495306 seconds.
  path: Assets/assets/PopulationSystem/CharacterPack01/Meshes/Farmer Tractor.FBX
  artifactKey: Guid(bcbca6b7ff30ede4ab80d57895589db4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/assets/PopulationSystem/CharacterPack01/Meshes/Farmer Tractor.FBX using Guid(bcbca6b7ff30ede4ab80d57895589db4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '34c07e173ef5b81b2801b6f5c0fb4327') in 0.096699 seconds 
Number of asset objects unloaded after import = 134
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014615 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.577 seconds
Domain Reload Profiling:
	ReloadAssembly (2578ms)
		BeginReloadAssembly (242ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (59ms)
		EndReloadAssembly (2159ms)
			LoadAssemblies (182ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (429ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (83ms)
			SetupLoadedEditorAssemblies (1432ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1200ms)
				ProcessInitializeOnLoadMethodAttributes (100ms)
				AfterProcessingInitializeOnLoad (14ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (12ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.64 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3724 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4317.
Memory consumption went from 264.1 MB to 263.4 MB.
Total: 7.309200 ms (FindLiveObjects: 0.534300 ms CreateObjectMapping: 0.263300 ms MarkObjects: 5.797800 ms  DeleteObjects: 0.712700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013923 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.432 seconds
Domain Reload Profiling:
	ReloadAssembly (2433ms)
		BeginReloadAssembly (241ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (56ms)
		EndReloadAssembly (2029ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (388ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (83ms)
			SetupLoadedEditorAssemblies (1359ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1143ms)
				ProcessInitializeOnLoadMethodAttributes (89ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3724 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4332.
Memory consumption went from 264.1 MB to 263.4 MB.
Total: 14.293400 ms (FindLiveObjects: 1.390200 ms CreateObjectMapping: 1.149500 ms MarkObjects: 10.961800 ms  DeleteObjects: 0.790100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014810 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.428 seconds
Domain Reload Profiling:
	ReloadAssembly (2429ms)
		BeginReloadAssembly (248ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (73ms)
		EndReloadAssembly (2027ms)
			LoadAssemblies (160ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (352ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (83ms)
			SetupLoadedEditorAssemblies (1397ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (92ms)
				ProcessInitializeOnLoadAttributes (1161ms)
				ProcessInitializeOnLoadMethodAttributes (105ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 4.09 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3724 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4347.
Memory consumption went from 264.2 MB to 263.5 MB.
Total: 8.952800 ms (FindLiveObjects: 0.562300 ms CreateObjectMapping: 0.299300 ms MarkObjects: 7.197600 ms  DeleteObjects: 0.892100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014366 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in 15.127 seconds
Domain Reload Profiling:
	ReloadAssembly (15129ms)
		BeginReloadAssembly (414ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (17ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (140ms)
		EndReloadAssembly (14553ms)
			LoadAssemblies (189ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (384ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (88ms)
			SetupLoadedEditorAssemblies (13878ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (13658ms)
				ProcessInitializeOnLoadMethodAttributes (89ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.65 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3724 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4362.
Memory consumption went from 264.2 MB to 263.5 MB.
Total: 9.614400 ms (FindLiveObjects: 0.517400 ms CreateObjectMapping: 0.262500 ms MarkObjects: 7.905800 ms  DeleteObjects: 0.927200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015801 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.423 seconds
Domain Reload Profiling:
	ReloadAssembly (2424ms)
		BeginReloadAssembly (248ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (2017ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (353ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (83ms)
			SetupLoadedEditorAssemblies (1389ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (92ms)
				ProcessInitializeOnLoadAttributes (1169ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.58 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3724 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4377.
Memory consumption went from 264.2 MB to 263.5 MB.
Total: 8.007300 ms (FindLiveObjects: 0.542100 ms CreateObjectMapping: 0.262800 ms MarkObjects: 6.343900 ms  DeleteObjects: 0.857300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016274 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.484 seconds
Domain Reload Profiling:
	ReloadAssembly (2485ms)
		BeginReloadAssembly (253ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (59ms)
		EndReloadAssembly (2068ms)
			LoadAssemblies (178ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (372ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1395ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (94ms)
				ProcessInitializeOnLoadAttributes (1147ms)
				ProcessInitializeOnLoadMethodAttributes (114ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.87 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3724 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4392.
Memory consumption went from 264.2 MB to 263.5 MB.
Total: 9.148900 ms (FindLiveObjects: 0.500000 ms CreateObjectMapping: 0.261700 ms MarkObjects: 7.538300 ms  DeleteObjects: 0.847600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
